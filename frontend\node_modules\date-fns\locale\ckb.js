"use strict";
exports.ckb = void 0;
var _index = require("./ckb/_lib/formatDistance.js");
var _index2 = require("./ckb/_lib/formatLong.js");
var _index3 = require("./ckb/_lib/formatRelative.js");
var _index4 = require("./ckb/_lib/localize.js");
var _index5 = require("./ckb/_lib/match.js");

/**
 * @type {Locale}
 * @category Locales
 * @summary Central Kurdish locale.
 * @language Central Kurdish
 * @iso-639-2 kur
 * <AUTHOR> [@Revan99]{@link https://github.com/Revan99}
 */
const ckb = (exports.ckb = {
  code: "ckb",
  formatDistance: _index.formatDistance,
  formatLong: _index2.formatLong,
  formatRelative: _index3.formatRelative,
  localize: _index4.localize,
  match: _index5.match,
  options: {
    weekStartsOn: 0 /* Sunday */,
    firstWeekContainsDate: 1,
  },
});
