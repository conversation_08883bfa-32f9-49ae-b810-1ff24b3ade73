var Q=function(E){return Q=typeof Symbol=="function"&&typeof Symbol.iterator=="symbol"?function(B){return typeof B}:function(B){return B&&typeof Symbol=="function"&&B.constructor===Symbol&&B!==Symbol.prototype?"symbol":typeof B},Q(E)},M=function(E,B){var G=Object.keys(E);if(Object.getOwnPropertySymbols){var Z=Object.getOwnPropertySymbols(E);B&&(Z=Z.filter(function(x){return Object.getOwnPropertyDescriptor(E,x).enumerable})),G.push.apply(G,Z)}return G},q=function(E){for(var B=1;B<arguments.length;B++){var G=arguments[B]!=null?arguments[B]:{};B%2?M(Object(G),!0).forEach(function(Z){C4(E,Z,G[Z])}):Object.getOwnPropertyDescriptors?Object.defineProperties(E,Object.getOwnPropertyDescriptors(G)):M(Object(G)).forEach(function(Z){Object.defineProperty(E,Z,Object.getOwnPropertyDescriptor(G,Z))})}return E},C4=function(E,B,G){if(B=U4(B),B in E)Object.defineProperty(E,B,{value:G,enumerable:!0,configurable:!0,writable:!0});else E[B]=G;return E},U4=function(E){var B=X4(E,"string");return Q(B)=="symbol"?B:String(B)},X4=function(E,B){if(Q(E)!="object"||!E)return E;var G=E[Symbol.toPrimitive];if(G!==void 0){var Z=G.call(E,B||"default");if(Q(Z)!="object")return Z;throw new TypeError("@@toPrimitive must return a primitive value.")}return(B==="string"?String:Number)(E)};(function(E){var B=Object.defineProperty,G=function C(X,U){for(var Y in U)B(X,Y,{get:U[Y],enumerable:!0,configurable:!0,set:function H(J){return U[Y]=function(){return J}}})},Z={lessThanXSeconds:{one:"1 \u0441\u043E\u043D\u0438\u044F\u0434\u0430\u043D \u043A\u0430\u043C",other:"{{count}} \u0441\u043E\u043D\u0438\u044F\u0434\u0430\u043D \u043A\u0430\u043C"},xSeconds:{one:"1 \u0441\u043E\u043D\u0438\u044F",other:"{{count}} \u0441\u043E\u043D\u0438\u044F"},halfAMinute:"\u044F\u0440\u0438\u043C \u0434\u0430\u049B\u0438\u049B\u0430",lessThanXMinutes:{one:"1 \u0434\u0430\u049B\u0438\u049B\u0430\u0434\u0430\u043D \u043A\u0430\u043C",other:"{{count}} \u0434\u0430\u049B\u0438\u049B\u0430\u0434\u0430\u043D \u043A\u0430\u043C"},xMinutes:{one:"1 \u0434\u0430\u049B\u0438\u049B\u0430",other:"{{count}} \u0434\u0430\u049B\u0438\u049B\u0430"},aboutXHours:{one:"\u0442\u0430\u0445\u043C\u0438\u043D\u0430\u043D 1 \u0441\u043E\u0430\u0442",other:"\u0442\u0430\u0445\u043C\u0438\u043D\u0430\u043D {{count}} \u0441\u043E\u0430\u0442"},xHours:{one:"1 \u0441\u043E\u0430\u0442",other:"{{count}} \u0441\u043E\u0430\u0442"},xDays:{one:"1 \u043A\u0443\u043D",other:"{{count}} \u043A\u0443\u043D"},aboutXWeeks:{one:"\u0442\u0430\u0445\u043C\u0438\u043D\u0430\u043D 1 \u0445\u0430\u0444\u0442\u0430",other:"\u0442\u0430\u0445\u043C\u0438\u043D\u0430\u043D {{count}} \u0445\u0430\u0444\u0442\u0430"},xWeeks:{one:"1 \u0445\u0430\u0444\u0442\u0430",other:"{{count}} \u0445\u0430\u0444\u0442\u0430"},aboutXMonths:{one:"\u0442\u0430\u0445\u043C\u0438\u043D\u0430\u043D 1 \u043E\u0439",other:"\u0442\u0430\u0445\u043C\u0438\u043D\u0430\u043D {{count}} \u043E\u0439"},xMonths:{one:"1 \u043E\u0439",other:"{{count}} \u043E\u0439"},aboutXYears:{one:"\u0442\u0430\u0445\u043C\u0438\u043D\u0430\u043D 1 \u0439\u0438\u043B",other:"\u0442\u0430\u0445\u043C\u0438\u043D\u0430\u043D {{count}} \u0439\u0438\u043B"},xYears:{one:"1 \u0439\u0438\u043B",other:"{{count}} \u0439\u0438\u043B"},overXYears:{one:"1 \u0439\u0438\u043B\u0434\u0430\u043D \u043A\u045E\u043F",other:"{{count}} \u0439\u0438\u043B\u0434\u0430\u043D \u043A\u045E\u043F"},almostXYears:{one:"\u0434\u0435\u044F\u0440\u043B\u0438 1 \u0439\u0438\u043B",other:"\u0434\u0435\u044F\u0440\u043B\u0438 {{count}} \u0439\u0438\u043B"}},x=function C(X,U,Y){var H,J=Z[X];if(typeof J==="string")H=J;else if(U===1)H=J.one;else H=J.other.replace("{{count}}",String(U));if(Y!==null&&Y!==void 0&&Y.addSuffix)if(Y.comparison&&Y.comparison>0)return H+"\u0434\u0430\u043D \u043A\u0435\u0439\u0438\u043D";else return H+" \u043E\u043B\u0434\u0438\u043D";return H};function K(C){return function(){var X=arguments.length>0&&arguments[0]!==void 0?arguments[0]:{},U=X.width?String(X.width):C.defaultWidth,Y=C.formats[U]||C.formats[C.defaultWidth];return Y}}var $={full:"EEEE, do MMMM, y",long:"do MMMM, y",medium:"d MMM, y",short:"dd/MM/yyyy"},z={full:"H:mm:ss zzzz",long:"H:mm:ss z",medium:"H:mm:ss",short:"H:mm"},R={any:"{{date}}, {{time}}"},S={date:K({formats:$,defaultWidth:"full"}),time:K({formats:z,defaultWidth:"full"}),dateTime:K({formats:R,defaultWidth:"any"})},L={lastWeek:"'\u045E\u0442\u0433\u0430\u043D' eeee p '\u0434\u0430'",yesterday:"'\u043A\u0435\u0447\u0430' p '\u0434\u0430'",today:"'\u0431\u0443\u0433\u0443\u043D' p '\u0434\u0430'",tomorrow:"'\u044D\u0440\u0442\u0430\u0433\u0430' p '\u0434\u0430'",nextWeek:"eeee p '\u0434\u0430'",other:"P"},V=function C(X,U,Y,H){return L[X]};function T(C){return function(X,U){var Y=U!==null&&U!==void 0&&U.context?String(U.context):"standalone",H;if(Y==="formatting"&&C.formattingValues){var J=C.defaultFormattingWidth||C.defaultWidth,A=U!==null&&U!==void 0&&U.width?String(U.width):J;H=C.formattingValues[A]||C.formattingValues[J]}else{var I=C.defaultWidth,W=U!==null&&U!==void 0&&U.width?String(U.width):C.defaultWidth;H=C.values[W]||C.values[I]}var O=C.argumentCallback?C.argumentCallback(X):X;return H[O]}}var f={narrow:["\u041C.\u0410","\u041C"],abbreviated:["\u041C.\u0410","\u041C"],wide:["\u041C\u0438\u043B\u043E\u0434\u0434\u0430\u043D \u0410\u0432\u0432\u0430\u043B\u0433\u0438","\u041C\u0438\u043B\u043E\u0434\u0438\u0439"]},j={narrow:["1","2","3","4"],abbreviated:["1-\u0447\u043E\u0440.","2-\u0447\u043E\u0440.","3-\u0447\u043E\u0440.","4-\u0447\u043E\u0440."],wide:["1-\u0447\u043E\u0440\u0430\u043A","2-\u0447\u043E\u0440\u0430\u043A","3-\u0447\u043E\u0440\u0430\u043A","4-\u0447\u043E\u0440\u0430\u043A"]},w={narrow:["\u042F","\u0424","\u041C","\u0410","\u041C","\u0418","\u0418","\u0410","\u0421","\u041E","\u041D","\u0414"],abbreviated:["\u044F\u043D\u0432","\u0444\u0435\u0432","\u043C\u0430\u0440","\u0430\u043F\u0440","\u043C\u0430\u0439","\u0438\u044E\u043D","\u0438\u044E\u043B","\u0430\u0432\u0433","\u0441\u0435\u043D","\u043E\u043A\u0442","\u043D\u043E\u044F","\u0434\u0435\u043A"],wide:["\u044F\u043D\u0432\u0430\u0440","\u0444\u0435\u0432\u0440\u0430\u043B","\u043C\u0430\u0440\u0442","\u0430\u043F\u0440\u0435\u043B","\u043C\u0430\u0439","\u0438\u044E\u043D","\u0438\u044E\u043B","\u0430\u0432\u0433\u0443\u0441\u0442","\u0441\u0435\u043D\u0442\u0430\u0431\u0440","\u043E\u043A\u0442\u0430\u0431\u0440","\u043D\u043E\u044F\u0431\u0440","\u0434\u0435\u043A\u0430\u0431\u0440"]},v={narrow:["\u042F","\u0414","\u0421","\u0427","\u041F","\u0416","\u0428"],short:["\u044F\u043A","\u0434\u0443","\u0441\u0435","\u0447\u043E","\u043F\u0430","\u0436\u0443","\u0448\u0430"],abbreviated:["\u044F\u043A\u0448","\u0434\u0443\u0448","\u0441\u0435\u0448","\u0447\u043E\u0440","\u043F\u0430\u0439","\u0436\u0443\u043C","\u0448\u0430\u043D"],wide:["\u044F\u043A\u0448\u0430\u043D\u0431\u0430","\u0434\u0443\u0448\u0430\u043D\u0431\u0430","\u0441\u0435\u0448\u0430\u043D\u0431\u0430","\u0447\u043E\u0440\u0448\u0430\u043D\u0431\u0430","\u043F\u0430\u0439\u0448\u0430\u043D\u0431\u0430","\u0436\u0443\u043C\u0430","\u0448\u0430\u043D\u0431\u0430"]},_={any:{am:"\u041F.\u041E.",pm:"\u041F.\u041A.",midnight:"\u044F\u0440\u0438\u043C \u0442\u0443\u043D",noon:"\u043F\u0435\u0448\u0438\u043D",morning:"\u044D\u0440\u0442\u0430\u043B\u0430\u0431",afternoon:"\u043F\u0435\u0448\u0438\u043D\u0434\u0430\u043D \u043A\u0435\u0439\u0438\u043D",evening:"\u043A\u0435\u0447\u0430\u0441\u0438",night:"\u0442\u0443\u043D"}},F={any:{am:"\u041F.\u041E.",pm:"\u041F.\u041A.",midnight:"\u044F\u0440\u0438\u043C \u0442\u0443\u043D",noon:"\u043F\u0435\u0448\u0438\u043D",morning:"\u044D\u0440\u0442\u0430\u043B\u0430\u0431",afternoon:"\u043F\u0435\u0448\u0438\u043D\u0434\u0430\u043D \u043A\u0435\u0439\u0438\u043D",evening:"\u043A\u0435\u0447\u0430\u0441\u0438",night:"\u0442\u0443\u043D"}},P=function C(X,U){return String(X)},b={ordinalNumber:P,era:T({values:f,defaultWidth:"wide"}),quarter:T({values:j,defaultWidth:"wide",argumentCallback:function C(X){return X-1}}),month:T({values:w,defaultWidth:"wide"}),day:T({values:v,defaultWidth:"wide"}),dayPeriod:T({values:_,defaultWidth:"any",formattingValues:F,defaultFormattingWidth:"any"})};function D(C){return function(X){var U=arguments.length>1&&arguments[1]!==void 0?arguments[1]:{},Y=U.width,H=Y&&C.matchPatterns[Y]||C.matchPatterns[C.defaultMatchWidth],J=X.match(H);if(!J)return null;var A=J[0],I=Y&&C.parsePatterns[Y]||C.parsePatterns[C.defaultParseWidth],W=Array.isArray(I)?h(I,function(N){return N.test(A)}):k(I,function(N){return N.test(A)}),O;O=C.valueCallback?C.valueCallback(W):W,O=U.valueCallback?U.valueCallback(O):O;var t=X.slice(A.length);return{value:O,rest:t}}}var k=function C(X,U){for(var Y in X)if(Object.prototype.hasOwnProperty.call(X,Y)&&U(X[Y]))return Y;return},h=function C(X,U){for(var Y=0;Y<X.length;Y++)if(U(X[Y]))return Y;return};function u(C){return function(X){var U=arguments.length>1&&arguments[1]!==void 0?arguments[1]:{},Y=X.match(C.matchPattern);if(!Y)return null;var H=Y[0],J=X.match(C.parsePattern);if(!J)return null;var A=C.valueCallback?C.valueCallback(J[0]):J[0];A=U.valueCallback?U.valueCallback(A):A;var I=X.slice(H.length);return{value:A,rest:I}}}var m=/^(\d+)(чи)?/i,c=/\d+/i,y={narrow:/^(м\.а|м\.)/i,abbreviated:/^(м\.а|м\.)/i,wide:/^(милоддан аввал|милоддан кейин)/i},p={any:[/^м/i,/^а/i]},g={narrow:/^[1234]/i,abbreviated:/^[1234]-чор./i,wide:/^[1234]-чорак/i},d={any:[/1/i,/2/i,/3/i,/4/i]},l={narrow:/^[яфмамииасонд]/i,abbreviated:/^(янв|фев|мар|апр|май|июн|июл|авг|сен|окт|ноя|дек)/i,wide:/^(январ|феврал|март|апрел|май|июн|июл|август|сентабр|октабр|ноябр|декабр)/i},i={narrow:[/^я/i,/^ф/i,/^м/i,/^а/i,/^м/i,/^и/i,/^и/i,/^а/i,/^с/i,/^о/i,/^н/i,/^д/i],any:[/^я/i,/^ф/i,/^мар/i,/^ап/i,/^май/i,/^июн/i,/^июл/i,/^ав/i,/^с/i,/^о/i,/^н/i,/^д/i]},n={narrow:/^[ядсчпжш]/i,short:/^(як|ду|се|чо|па|жу|ша)/i,abbreviated:/^(якш|душ|сеш|чор|пай|жум|шан)/i,wide:/^(якшанба|душанба|сешанба|чоршанба|пайшанба|жума|шанба)/i},s={narrow:[/^я/i,/^д/i,/^с/i,/^ч/i,/^п/i,/^ж/i,/^ш/i],any:[/^як/i,/^ду/i,/^се/i,/^чор/i,/^пай/i,/^жу/i,/^шан/i]},o={any:/^(п\.о\.|п\.к\.|ярим тун|пешиндан кейин|(эрталаб|пешиндан кейин|кечаси|тун))/i},r={any:{am:/^п\.о\./i,pm:/^п\.к\./i,midnight:/^ярим тун/i,noon:/^пешиндан кейин/i,morning:/эрталаб/i,afternoon:/пешиндан кейин/i,evening:/кечаси/i,night:/тун/i}},e={ordinalNumber:u({matchPattern:m,parsePattern:c,valueCallback:function C(X){return parseInt(X,10)}}),era:D({matchPatterns:y,defaultMatchWidth:"wide",parsePatterns:p,defaultParseWidth:"any"}),quarter:D({matchPatterns:g,defaultMatchWidth:"wide",parsePatterns:d,defaultParseWidth:"any",valueCallback:function C(X){return X+1}}),month:D({matchPatterns:l,defaultMatchWidth:"wide",parsePatterns:i,defaultParseWidth:"any"}),day:D({matchPatterns:n,defaultMatchWidth:"wide",parsePatterns:s,defaultParseWidth:"any"}),dayPeriod:D({matchPatterns:o,defaultMatchWidth:"any",parsePatterns:r,defaultParseWidth:"any"})},a={code:"uz-Cyrl",formatDistance:x,formatLong:S,formatRelative:V,localize:b,match:e,options:{weekStartsOn:1,firstWeekContainsDate:1}};window.dateFns=q(q({},window.dateFns),{},{locale:q(q({},(E=window.dateFns)===null||E===void 0?void 0:E.locale),{},{uzCyrl:a})})})();

//# debugId=E5EDC933B4B11A4D64756e2164756e21
