var P=function(U,J){var I=Object.keys(U);if(Object.getOwnPropertySymbols){var Y=Object.getOwnPropertySymbols(U);J&&(Y=Y.filter(function(Z){return Object.getOwnPropertyDescriptor(U,Z).enumerable})),I.push.apply(I,Y)}return I},T=function(U){for(var J=1;J<arguments.length;J++){var I=arguments[J]!=null?arguments[J]:{};J%2?P(Object(I),!0).forEach(function(Y){M0(U,Y,I[Y])}):Object.getOwnPropertyDescriptors?Object.defineProperties(U,Object.getOwnPropertyDescriptors(I)):P(Object(I)).forEach(function(Y){Object.defineProperty(U,Y,Object.getOwnPropertyDescriptor(I,Y))})}return U},M0=function(U,J,I){if(J=V0(J),J in U)Object.defineProperty(U,J,{value:I,enumerable:!0,configurable:!0,writable:!0});else U[J]=I;return U},V0=function(U){var J=R0(U,"string");return V(J)=="symbol"?J:String(J)},R0=function(U,J){if(V(U)!="object"||!U)return U;var I=U[Symbol.toPrimitive];if(I!==void 0){var Y=I.call(U,J||"default");if(V(Y)!="object")return Y;throw new TypeError("@@toPrimitive must return a primitive value.")}return(J==="string"?String:Number)(U)},V=function(U){return V=typeof Symbol=="function"&&typeof Symbol.iterator=="symbol"?function(J){return typeof J}:function(J){return J&&typeof Symbol=="function"&&J.constructor===Symbol&&J!==Symbol.prototype?"symbol":typeof J},V(U)};(function(U){var J=Object.defineProperty,I=function G(B,C){for(var H in C)J(B,H,{get:C[H],enumerable:!0,configurable:!0,set:function E(X){return C[H]=function(){return X}}})},Y=function G(B,C){if(B.one!==void 0&&C===1)return B.one;var H=C%10,E=C%100;if(H===1&&E!==11)return B.singularNominative.replace("{{count}}",String(C));else if(H>=2&&H<=4&&(E<10||E>20))return B.singularGenitive.replace("{{count}}",String(C));else return B.pluralGenitive.replace("{{count}}",String(C))},Z=function G(B){return function(C,H){if(H&&H.addSuffix)if(H.comparison&&H.comparison>0)if(B.future)return Y(B.future,C);else return"\u0437\u0430 "+Y(B.regular,C);else if(B.past)return Y(B.past,C);else return Y(B.regular,C)+" \u0442\u043E\u043C\u0443";else return Y(B.regular,C)}},v=function G(B,C){if(C&&C.addSuffix)if(C.comparison&&C.comparison>0)return"\u0437\u0430 \u043F\u0456\u0432\u0445\u0432\u0438\u043B\u0438\u043D\u0438";else return"\u043F\u0456\u0432\u0445\u0432\u0438\u043B\u0438\u043D\u0438 \u0442\u043E\u043C\u0443";return"\u043F\u0456\u0432\u0445\u0432\u0438\u043B\u0438\u043D\u0438"},L={lessThanXSeconds:Z({regular:{one:"\u043C\u0435\u043D\u0448\u0435 \u0441\u0435\u043A\u0443\u043D\u0434\u0438",singularNominative:"\u043C\u0435\u043D\u0448\u0435 {{count}} \u0441\u0435\u043A\u0443\u043D\u0434\u0438",singularGenitive:"\u043C\u0435\u043D\u0448\u0435 {{count}} \u0441\u0435\u043A\u0443\u043D\u0434",pluralGenitive:"\u043C\u0435\u043D\u0448\u0435 {{count}} \u0441\u0435\u043A\u0443\u043D\u0434"},future:{one:"\u043C\u0435\u043D\u0448\u0435, \u043D\u0456\u0436 \u0437\u0430 \u0441\u0435\u043A\u0443\u043D\u0434\u0443",singularNominative:"\u043C\u0435\u043D\u0448\u0435, \u043D\u0456\u0436 \u0437\u0430 {{count}} \u0441\u0435\u043A\u0443\u043D\u0434\u0443",singularGenitive:"\u043C\u0435\u043D\u0448\u0435, \u043D\u0456\u0436 \u0437\u0430 {{count}} \u0441\u0435\u043A\u0443\u043D\u0434\u0438",pluralGenitive:"\u043C\u0435\u043D\u0448\u0435, \u043D\u0456\u0436 \u0437\u0430 {{count}} \u0441\u0435\u043A\u0443\u043D\u0434"}}),xSeconds:Z({regular:{singularNominative:"{{count}} \u0441\u0435\u043A\u0443\u043D\u0434\u0430",singularGenitive:"{{count}} \u0441\u0435\u043A\u0443\u043D\u0434\u0438",pluralGenitive:"{{count}} \u0441\u0435\u043A\u0443\u043D\u0434"},past:{singularNominative:"{{count}} \u0441\u0435\u043A\u0443\u043D\u0434\u0443 \u0442\u043E\u043C\u0443",singularGenitive:"{{count}} \u0441\u0435\u043A\u0443\u043D\u0434\u0438 \u0442\u043E\u043C\u0443",pluralGenitive:"{{count}} \u0441\u0435\u043A\u0443\u043D\u0434 \u0442\u043E\u043C\u0443"},future:{singularNominative:"\u0437\u0430 {{count}} \u0441\u0435\u043A\u0443\u043D\u0434\u0443",singularGenitive:"\u0437\u0430 {{count}} \u0441\u0435\u043A\u0443\u043D\u0434\u0438",pluralGenitive:"\u0437\u0430 {{count}} \u0441\u0435\u043A\u0443\u043D\u0434"}}),halfAMinute:v,lessThanXMinutes:Z({regular:{one:"\u043C\u0435\u043D\u0448\u0435 \u0445\u0432\u0438\u043B\u0438\u043D\u0438",singularNominative:"\u043C\u0435\u043D\u0448\u0435 {{count}} \u0445\u0432\u0438\u043B\u0438\u043D\u0438",singularGenitive:"\u043C\u0435\u043D\u0448\u0435 {{count}} \u0445\u0432\u0438\u043B\u0438\u043D",pluralGenitive:"\u043C\u0435\u043D\u0448\u0435 {{count}} \u0445\u0432\u0438\u043B\u0438\u043D"},future:{one:"\u043C\u0435\u043D\u0448\u0435, \u043D\u0456\u0436 \u0437\u0430 \u0445\u0432\u0438\u043B\u0438\u043D\u0443",singularNominative:"\u043C\u0435\u043D\u0448\u0435, \u043D\u0456\u0436 \u0437\u0430 {{count}} \u0445\u0432\u0438\u043B\u0438\u043D\u0443",singularGenitive:"\u043C\u0435\u043D\u0448\u0435, \u043D\u0456\u0436 \u0437\u0430 {{count}} \u0445\u0432\u0438\u043B\u0438\u043D\u0438",pluralGenitive:"\u043C\u0435\u043D\u0448\u0435, \u043D\u0456\u0436 \u0437\u0430 {{count}} \u0445\u0432\u0438\u043B\u0438\u043D"}}),xMinutes:Z({regular:{singularNominative:"{{count}} \u0445\u0432\u0438\u043B\u0438\u043D\u0430",singularGenitive:"{{count}} \u0445\u0432\u0438\u043B\u0438\u043D\u0438",pluralGenitive:"{{count}} \u0445\u0432\u0438\u043B\u0438\u043D"},past:{singularNominative:"{{count}} \u0445\u0432\u0438\u043B\u0438\u043D\u0443 \u0442\u043E\u043C\u0443",singularGenitive:"{{count}} \u0445\u0432\u0438\u043B\u0438\u043D\u0438 \u0442\u043E\u043C\u0443",pluralGenitive:"{{count}} \u0445\u0432\u0438\u043B\u0438\u043D \u0442\u043E\u043C\u0443"},future:{singularNominative:"\u0437\u0430 {{count}} \u0445\u0432\u0438\u043B\u0438\u043D\u0443",singularGenitive:"\u0437\u0430 {{count}} \u0445\u0432\u0438\u043B\u0438\u043D\u0438",pluralGenitive:"\u0437\u0430 {{count}} \u0445\u0432\u0438\u043B\u0438\u043D"}}),aboutXHours:Z({regular:{singularNominative:"\u0431\u043B\u0438\u0437\u044C\u043A\u043E {{count}} \u0433\u043E\u0434\u0438\u043D\u0438",singularGenitive:"\u0431\u043B\u0438\u0437\u044C\u043A\u043E {{count}} \u0433\u043E\u0434\u0438\u043D",pluralGenitive:"\u0431\u043B\u0438\u0437\u044C\u043A\u043E {{count}} \u0433\u043E\u0434\u0438\u043D"},future:{singularNominative:"\u043F\u0440\u0438\u0431\u043B\u0438\u0437\u043D\u043E \u0437\u0430 {{count}} \u0433\u043E\u0434\u0438\u043D\u0443",singularGenitive:"\u043F\u0440\u0438\u0431\u043B\u0438\u0437\u043D\u043E \u0437\u0430 {{count}} \u0433\u043E\u0434\u0438\u043D\u0438",pluralGenitive:"\u043F\u0440\u0438\u0431\u043B\u0438\u0437\u043D\u043E \u0437\u0430 {{count}} \u0433\u043E\u0434\u0438\u043D"}}),xHours:Z({regular:{singularNominative:"{{count}} \u0433\u043E\u0434\u0438\u043D\u0443",singularGenitive:"{{count}} \u0433\u043E\u0434\u0438\u043D\u0438",pluralGenitive:"{{count}} \u0433\u043E\u0434\u0438\u043D"}}),xDays:Z({regular:{singularNominative:"{{count}} \u0434\u0435\u043D\u044C",singularGenitive:"{{count}} \u0434\u043Di",pluralGenitive:"{{count}} \u0434\u043D\u0456\u0432"}}),aboutXWeeks:Z({regular:{singularNominative:"\u0431\u043B\u0438\u0437\u044C\u043A\u043E {{count}} \u0442\u0438\u0436\u043D\u044F",singularGenitive:"\u0431\u043B\u0438\u0437\u044C\u043A\u043E {{count}} \u0442\u0438\u0436\u043D\u0456\u0432",pluralGenitive:"\u0431\u043B\u0438\u0437\u044C\u043A\u043E {{count}} \u0442\u0438\u0436\u043D\u0456\u0432"},future:{singularNominative:"\u043F\u0440\u0438\u0431\u043B\u0438\u0437\u043D\u043E \u0437\u0430 {{count}} \u0442\u0438\u0436\u0434\u0435\u043D\u044C",singularGenitive:"\u043F\u0440\u0438\u0431\u043B\u0438\u0437\u043D\u043E \u0437\u0430 {{count}} \u0442\u0438\u0436\u043D\u0456",pluralGenitive:"\u043F\u0440\u0438\u0431\u043B\u0438\u0437\u043D\u043E \u0437\u0430 {{count}} \u0442\u0438\u0436\u043D\u0456\u0432"}}),xWeeks:Z({regular:{singularNominative:"{{count}} \u0442\u0438\u0436\u0434\u0435\u043D\u044C",singularGenitive:"{{count}} \u0442\u0438\u0436\u043D\u0456",pluralGenitive:"{{count}} \u0442\u0438\u0436\u043D\u0456\u0432"}}),aboutXMonths:Z({regular:{singularNominative:"\u0431\u043B\u0438\u0437\u044C\u043A\u043E {{count}} \u043C\u0456\u0441\u044F\u0446\u044F",singularGenitive:"\u0431\u043B\u0438\u0437\u044C\u043A\u043E {{count}} \u043C\u0456\u0441\u044F\u0446\u0456\u0432",pluralGenitive:"\u0431\u043B\u0438\u0437\u044C\u043A\u043E {{count}} \u043C\u0456\u0441\u044F\u0446\u0456\u0432"},future:{singularNominative:"\u043F\u0440\u0438\u0431\u043B\u0438\u0437\u043D\u043E \u0437\u0430 {{count}} \u043C\u0456\u0441\u044F\u0446\u044C",singularGenitive:"\u043F\u0440\u0438\u0431\u043B\u0438\u0437\u043D\u043E \u0437\u0430 {{count}} \u043C\u0456\u0441\u044F\u0446\u0456",pluralGenitive:"\u043F\u0440\u0438\u0431\u043B\u0438\u0437\u043D\u043E \u0437\u0430 {{count}} \u043C\u0456\u0441\u044F\u0446\u0456\u0432"}}),xMonths:Z({regular:{singularNominative:"{{count}} \u043C\u0456\u0441\u044F\u0446\u044C",singularGenitive:"{{count}} \u043C\u0456\u0441\u044F\u0446\u0456",pluralGenitive:"{{count}} \u043C\u0456\u0441\u044F\u0446\u0456\u0432"}}),aboutXYears:Z({regular:{singularNominative:"\u0431\u043B\u0438\u0437\u044C\u043A\u043E {{count}} \u0440\u043E\u043A\u0443",singularGenitive:"\u0431\u043B\u0438\u0437\u044C\u043A\u043E {{count}} \u0440\u043E\u043A\u0456\u0432",pluralGenitive:"\u0431\u043B\u0438\u0437\u044C\u043A\u043E {{count}} \u0440\u043E\u043A\u0456\u0432"},future:{singularNominative:"\u043F\u0440\u0438\u0431\u043B\u0438\u0437\u043D\u043E \u0437\u0430 {{count}} \u0440\u0456\u043A",singularGenitive:"\u043F\u0440\u0438\u0431\u043B\u0438\u0437\u043D\u043E \u0437\u0430 {{count}} \u0440\u043E\u043A\u0438",pluralGenitive:"\u043F\u0440\u0438\u0431\u043B\u0438\u0437\u043D\u043E \u0437\u0430 {{count}} \u0440\u043E\u043A\u0456\u0432"}}),xYears:Z({regular:{singularNominative:"{{count}} \u0440\u0456\u043A",singularGenitive:"{{count}} \u0440\u043E\u043A\u0438",pluralGenitive:"{{count}} \u0440\u043E\u043A\u0456\u0432"}}),overXYears:Z({regular:{singularNominative:"\u0431\u0456\u043B\u044C\u0448\u0435 {{count}} \u0440\u043E\u043A\u0443",singularGenitive:"\u0431\u0456\u043B\u044C\u0448\u0435 {{count}} \u0440\u043E\u043A\u0456\u0432",pluralGenitive:"\u0431\u0456\u043B\u044C\u0448\u0435 {{count}} \u0440\u043E\u043A\u0456\u0432"},future:{singularNominative:"\u0431\u0456\u043B\u044C\u0448\u0435, \u043D\u0456\u0436 \u0437\u0430 {{count}} \u0440\u0456\u043A",singularGenitive:"\u0431\u0456\u043B\u044C\u0448\u0435, \u043D\u0456\u0436 \u0437\u0430 {{count}} \u0440\u043E\u043A\u0438",pluralGenitive:"\u0431\u0456\u043B\u044C\u0448\u0435, \u043D\u0456\u0436 \u0437\u0430 {{count}} \u0440\u043E\u043A\u0456\u0432"}}),almostXYears:Z({regular:{singularNominative:"\u043C\u0430\u0439\u0436\u0435 {{count}} \u0440\u0456\u043A",singularGenitive:"\u043C\u0430\u0439\u0436\u0435 {{count}} \u0440\u043E\u043A\u0438",pluralGenitive:"\u043C\u0430\u0439\u0436\u0435 {{count}} \u0440\u043E\u043A\u0456\u0432"},future:{singularNominative:"\u043C\u0430\u0439\u0436\u0435 \u0437\u0430 {{count}} \u0440\u0456\u043A",singularGenitive:"\u043C\u0430\u0439\u0436\u0435 \u0437\u0430 {{count}} \u0440\u043E\u043A\u0438",pluralGenitive:"\u043C\u0430\u0439\u0436\u0435 \u0437\u0430 {{count}} \u0440\u043E\u043A\u0456\u0432"}})},w=function G(B,C,H){return H=H||{},L[B](C,H)};function j(G){return function(){var B=arguments.length>0&&arguments[0]!==void 0?arguments[0]:{},C=B.width?String(B.width):G.defaultWidth,H=G.formats[C]||G.formats[G.defaultWidth];return H}}var F={full:"EEEE, do MMMM y '\u0440.'",long:"do MMMM y '\u0440.'",medium:"d MMM y '\u0440.'",short:"dd.MM.y"},_={full:"H:mm:ss zzzz",long:"H:mm:ss z",medium:"H:mm:ss",short:"H:mm"},b={full:"{{date}} '\u043E' {{time}}",long:"{{date}} '\u043E' {{time}}",medium:"{{date}}, {{time}}",short:"{{date}}, {{time}}"},f={date:j({formats:F,defaultWidth:"full"}),time:j({formats:_,defaultWidth:"full"}),dateTime:j({formats:b,defaultWidth:"full"})};function D(G){var B=Object.prototype.toString.call(G);if(G instanceof Date||V(G)==="object"&&B==="[object Date]")return new G.constructor(+G);else if(typeof G==="number"||B==="[object Number]"||typeof G==="string"||B==="[object String]")return new Date(G);else return new Date(NaN)}function h(){return O}function x0(G){O=G}var O={};function W(G,B){var C,H,E,X,A,q,K=h(),Q=(C=(H=(E=(X=B===null||B===void 0?void 0:B.weekStartsOn)!==null&&X!==void 0?X:B===null||B===void 0||(A=B.locale)===null||A===void 0||(A=A.options)===null||A===void 0?void 0:A.weekStartsOn)!==null&&E!==void 0?E:K.weekStartsOn)!==null&&H!==void 0?H:(q=K.locale)===null||q===void 0||(q=q.options)===null||q===void 0?void 0:q.weekStartsOn)!==null&&C!==void 0?C:0,N=D(G),M=N.getDay(),N0=(M<Q?7:0)+M-Q;return N.setDate(N.getDate()-N0),N.setHours(0,0,0,0),N}function S(G,B,C){var H=W(G,C),E=W(B,C);return+H===+E}var k=function G(B){var C=z[B];switch(B){case 0:case 3:case 5:case 6:return"'\u0443 \u043C\u0438\u043D\u0443\u043B\u0443 "+C+" \u043E' p";case 1:case 2:case 4:return"'\u0443 \u043C\u0438\u043D\u0443\u043B\u0438\u0439 "+C+" \u043E' p"}},$=function G(B){var C=z[B];return"'\u0443 "+C+" \u043E' p"},u=function G(B){var C=z[B];switch(B){case 0:case 3:case 5:case 6:return"'\u0443 \u043D\u0430\u0441\u0442\u0443\u043F\u043D\u0443 "+C+" \u043E' p";case 1:case 2:case 4:return"'\u0443 \u043D\u0430\u0441\u0442\u0443\u043F\u043D\u0438\u0439 "+C+" \u043E' p"}},z=["\u043D\u0435\u0434\u0456\u043B\u044E","\u043F\u043E\u043D\u0435\u0434\u0456\u043B\u043E\u043A","\u0432\u0456\u0432\u0442\u043E\u0440\u043E\u043A","\u0441\u0435\u0440\u0435\u0434\u0443","\u0447\u0435\u0442\u0432\u0435\u0440","\u043F\u2019\u044F\u0442\u043D\u0438\u0446\u044E","\u0441\u0443\u0431\u043E\u0442\u0443"],y=function G(B,C,H){var E=D(B),X=E.getDay();if(S(E,C,H))return $(X);else return k(X)},g=function G(B,C,H){var E=D(B),X=E.getDay();if(S(E,C,H))return $(X);else return u(X)},m={lastWeek:y,yesterday:"'\u0432\u0447\u043E\u0440\u0430 \u043E' p",today:"'\u0441\u044C\u043E\u0433\u043E\u0434\u043D\u0456 \u043E' p",tomorrow:"'\u0437\u0430\u0432\u0442\u0440\u0430 \u043E' p",nextWeek:g,other:"P"},c=function G(B,C,H,E){var X=m[B];if(typeof X==="function")return X(C,H,E);return X};function R(G){return function(B,C){var H=C!==null&&C!==void 0&&C.context?String(C.context):"standalone",E;if(H==="formatting"&&G.formattingValues){var X=G.defaultFormattingWidth||G.defaultWidth,A=C!==null&&C!==void 0&&C.width?String(C.width):X;E=G.formattingValues[A]||G.formattingValues[X]}else{var q=G.defaultWidth,K=C!==null&&C!==void 0&&C.width?String(C.width):G.defaultWidth;E=G.values[K]||G.values[q]}var Q=G.argumentCallback?G.argumentCallback(B):B;return E[Q]}}var p={narrow:["\u0434\u043E \u043D.\u0435.","\u043D.\u0435."],abbreviated:["\u0434\u043E \u043D. \u0435.","\u043D. \u0435."],wide:["\u0434\u043E \u043D\u0430\u0448\u043E\u0457 \u0435\u0440\u0438","\u043D\u0430\u0448\u043E\u0457 \u0435\u0440\u0438"]},l={narrow:["1","2","3","4"],abbreviated:["1-\u0439 \u043A\u0432.","2-\u0439 \u043A\u0432.","3-\u0439 \u043A\u0432.","4-\u0439 \u043A\u0432."],wide:["1-\u0439 \u043A\u0432\u0430\u0440\u0442\u0430\u043B","2-\u0439 \u043A\u0432\u0430\u0440\u0442\u0430\u043B","3-\u0439 \u043A\u0432\u0430\u0440\u0442\u0430\u043B","4-\u0439 \u043A\u0432\u0430\u0440\u0442\u0430\u043B"]},d={narrow:["\u0421","\u041B","\u0411","\u041A","\u0422","\u0427","\u041B","\u0421","\u0412","\u0416","\u041B","\u0413"],abbreviated:["\u0441\u0456\u0447.","\u043B\u044E\u0442.","\u0431\u0435\u0440\u0435\u0437.","\u043A\u0432\u0456\u0442.","\u0442\u0440\u0430\u0432.","\u0447\u0435\u0440\u0432.","\u043B\u0438\u043F.","\u0441\u0435\u0440\u043F.","\u0432\u0435\u0440\u0435\u0441.","\u0436\u043E\u0432\u0442.","\u043B\u0438\u0441\u0442\u043E\u043F.","\u0433\u0440\u0443\u0434."],wide:["\u0441\u0456\u0447\u0435\u043D\u044C","\u043B\u044E\u0442\u0438\u0439","\u0431\u0435\u0440\u0435\u0437\u0435\u043D\u044C","\u043A\u0432\u0456\u0442\u0435\u043D\u044C","\u0442\u0440\u0430\u0432\u0435\u043D\u044C","\u0447\u0435\u0440\u0432\u0435\u043D\u044C","\u043B\u0438\u043F\u0435\u043D\u044C","\u0441\u0435\u0440\u043F\u0435\u043D\u044C","\u0432\u0435\u0440\u0435\u0441\u0435\u043D\u044C","\u0436\u043E\u0432\u0442\u0435\u043D\u044C","\u043B\u0438\u0441\u0442\u043E\u043F\u0430\u0434","\u0433\u0440\u0443\u0434\u0435\u043D\u044C"]},i={narrow:["\u0421","\u041B","\u0411","\u041A","\u0422","\u0427","\u041B","\u0421","\u0412","\u0416","\u041B","\u0413"],abbreviated:["\u0441\u0456\u0447.","\u043B\u044E\u0442.","\u0431\u0435\u0440\u0435\u0437.","\u043A\u0432\u0456\u0442.","\u0442\u0440\u0430\u0432.","\u0447\u0435\u0440\u0432.","\u043B\u0438\u043F.","\u0441\u0435\u0440\u043F.","\u0432\u0435\u0440\u0435\u0441.","\u0436\u043E\u0432\u0442.","\u043B\u0438\u0441\u0442\u043E\u043F.","\u0433\u0440\u0443\u0434."],wide:["\u0441\u0456\u0447\u043D\u044F","\u043B\u044E\u0442\u043E\u0433\u043E","\u0431\u0435\u0440\u0435\u0437\u043D\u044F","\u043A\u0432\u0456\u0442\u043D\u044F","\u0442\u0440\u0430\u0432\u043D\u044F","\u0447\u0435\u0440\u0432\u043D\u044F","\u043B\u0438\u043F\u043D\u044F","\u0441\u0435\u0440\u043F\u043D\u044F","\u0432\u0435\u0440\u0435\u0441\u043D\u044F","\u0436\u043E\u0432\u0442\u043D\u044F","\u043B\u0438\u0441\u0442\u043E\u043F\u0430\u0434\u0430","\u0433\u0440\u0443\u0434\u043D\u044F"]},r={narrow:["\u041D","\u041F","\u0412","\u0421","\u0427","\u041F","\u0421"],short:["\u043D\u0434","\u043F\u043D","\u0432\u0442","\u0441\u0440","\u0447\u0442","\u043F\u0442","\u0441\u0431"],abbreviated:["\u043D\u0435\u0434","\u043F\u043E\u043D","\u0432\u0456\u0432","\u0441\u0435\u0440","\u0447\u0442\u0432","\u043F\u0442\u043D","\u0441\u0443\u0431"],wide:["\u043D\u0435\u0434\u0456\u043B\u044F","\u043F\u043E\u043D\u0435\u0434\u0456\u043B\u043E\u043A","\u0432\u0456\u0432\u0442\u043E\u0440\u043E\u043A","\u0441\u0435\u0440\u0435\u0434\u0430","\u0447\u0435\u0442\u0432\u0435\u0440","\u043F\u2019\u044F\u0442\u043D\u0438\u0446\u044F","\u0441\u0443\u0431\u043E\u0442\u0430"]},n={narrow:{am:"\u0414\u041F",pm:"\u041F\u041F",midnight:"\u043F\u0456\u0432\u043D.",noon:"\u043F\u043E\u043B.",morning:"\u0440\u0430\u043D\u043E\u043A",afternoon:"\u0434\u0435\u043D\u044C",evening:"\u0432\u0435\u0447.",night:"\u043D\u0456\u0447"},abbreviated:{am:"\u0414\u041F",pm:"\u041F\u041F",midnight:"\u043F\u0456\u0432\u043D.",noon:"\u043F\u043E\u043B.",morning:"\u0440\u0430\u043D\u043E\u043A",afternoon:"\u0434\u0435\u043D\u044C",evening:"\u0432\u0435\u0447.",night:"\u043D\u0456\u0447"},wide:{am:"\u0414\u041F",pm:"\u041F\u041F",midnight:"\u043F\u0456\u0432\u043D\u0456\u0447",noon:"\u043F\u043E\u043B\u0443\u0434\u0435\u043D\u044C",morning:"\u0440\u0430\u043D\u043E\u043A",afternoon:"\u0434\u0435\u043D\u044C",evening:"\u0432\u0435\u0447\u0456\u0440",night:"\u043D\u0456\u0447"}},s={narrow:{am:"\u0414\u041F",pm:"\u041F\u041F",midnight:"\u043F\u0456\u0432\u043D.",noon:"\u043F\u043E\u043B.",morning:"\u0440\u0430\u043D\u043A\u0443",afternoon:"\u0434\u043D\u044F",evening:"\u0432\u0435\u0447.",night:"\u043D\u043E\u0447\u0456"},abbreviated:{am:"\u0414\u041F",pm:"\u041F\u041F",midnight:"\u043F\u0456\u0432\u043D.",noon:"\u043F\u043E\u043B.",morning:"\u0440\u0430\u043D\u043A\u0443",afternoon:"\u0434\u043D\u044F",evening:"\u0432\u0435\u0447.",night:"\u043D\u043E\u0447\u0456"},wide:{am:"\u0414\u041F",pm:"\u041F\u041F",midnight:"\u043F\u0456\u0432\u043D\u0456\u0447",noon:"\u043F\u043E\u043B\u0443\u0434\u0435\u043D\u044C",morning:"\u0440\u0430\u043D\u043A\u0443",afternoon:"\u0434\u043D\u044F",evening:"\u0432\u0435\u0447.",night:"\u043D\u043E\u0447\u0456"}},a=function G(B,C){var H=String(C===null||C===void 0?void 0:C.unit),E=Number(B),X;if(H==="date")if(E===3||E===23)X="-\u0454";else X="-\u0435";else if(H==="minute"||H==="second"||H==="hour")X="-\u0430";else X="-\u0439";return E+X},o={ordinalNumber:a,era:R({values:p,defaultWidth:"wide"}),quarter:R({values:l,defaultWidth:"wide",argumentCallback:function G(B){return B-1}}),month:R({values:d,defaultWidth:"wide",formattingValues:i,defaultFormattingWidth:"wide"}),day:R({values:r,defaultWidth:"wide"}),dayPeriod:R({values:n,defaultWidth:"any",formattingValues:s,defaultFormattingWidth:"wide"})};function x(G){return function(B){var C=arguments.length>1&&arguments[1]!==void 0?arguments[1]:{},H=C.width,E=H&&G.matchPatterns[H]||G.matchPatterns[G.defaultMatchWidth],X=B.match(E);if(!X)return null;var A=X[0],q=H&&G.parsePatterns[H]||G.parsePatterns[G.defaultParseWidth],K=Array.isArray(q)?e(q,function(M){return M.test(A)}):t(q,function(M){return M.test(A)}),Q;Q=G.valueCallback?G.valueCallback(K):K,Q=C.valueCallback?C.valueCallback(Q):Q;var N=B.slice(A.length);return{value:Q,rest:N}}}var t=function G(B,C){for(var H in B)if(Object.prototype.hasOwnProperty.call(B,H)&&C(B[H]))return H;return},e=function G(B,C){for(var H=0;H<B.length;H++)if(C(B[H]))return H;return};function C0(G){return function(B){var C=arguments.length>1&&arguments[1]!==void 0?arguments[1]:{},H=B.match(G.matchPattern);if(!H)return null;var E=H[0],X=B.match(G.parsePattern);if(!X)return null;var A=G.valueCallback?G.valueCallback(X[0]):X[0];A=C.valueCallback?C.valueCallback(A):A;var q=B.slice(E.length);return{value:A,rest:q}}}var B0=/^(\d+)(-?(е|й|є|а|я))?/i,G0=/\d+/i,H0={narrow:/^((до )?н\.?\s?е\.?)/i,abbreviated:/^((до )?н\.?\s?е\.?)/i,wide:/^(до нашої ери|нашої ери|наша ера)/i},X0={any:[/^д/i,/^н/i]},E0={narrow:/^[1234]/i,abbreviated:/^[1234](-?[иі]?й?)? кв.?/i,wide:/^[1234](-?[иі]?й?)? квартал/i},J0={any:[/1/i,/2/i,/3/i,/4/i]},U0={narrow:/^[слбктчвжг]/i,abbreviated:/^(січ|лют|бер(ез)?|квіт|трав|черв|лип|серп|вер(ес)?|жовт|лис(топ)?|груд)\.?/i,wide:/^(січень|січня|лютий|лютого|березень|березня|квітень|квітня|травень|травня|червня|червень|липень|липня|серпень|серпня|вересень|вересня|жовтень|жовтня|листопад[а]?|грудень|грудня)/i},Y0={narrow:[/^с/i,/^л/i,/^б/i,/^к/i,/^т/i,/^ч/i,/^л/i,/^с/i,/^в/i,/^ж/i,/^л/i,/^г/i],any:[/^сі/i,/^лю/i,/^б/i,/^к/i,/^т/i,/^ч/i,/^лип/i,/^се/i,/^в/i,/^ж/i,/^лис/i,/^г/i]},Z0={narrow:/^[нпвсч]/i,short:/^(нд|пн|вт|ср|чт|пт|сб)\.?/i,abbreviated:/^(нед|пон|вів|сер|че?тв|птн?|суб)\.?/i,wide:/^(неділ[яі]|понеділ[ок][ка]|вівтор[ок][ка]|серед[аи]|четвер(га)?|п\W*?ятниц[яі]|субот[аи])/i},A0={narrow:[/^н/i,/^п/i,/^в/i,/^с/i,/^ч/i,/^п/i,/^с/i],any:[/^н/i,/^п[он]/i,/^в/i,/^с[ер]/i,/^ч/i,/^п\W*?[ят]/i,/^с[уб]/i]},I0={narrow:/^([дп]п|півн\.?|пол\.?|ранок|ранку|день|дня|веч\.?|ніч|ночі)/i,abbreviated:/^([дп]п|півн\.?|пол\.?|ранок|ранку|день|дня|веч\.?|ніч|ночі)/i,wide:/^([дп]п|північ|полудень|ранок|ранку|день|дня|вечір|вечора|ніч|ночі)/i},q0={any:{am:/^дп/i,pm:/^пп/i,midnight:/^півн/i,noon:/^пол/i,morning:/^р/i,afternoon:/^д[ен]/i,evening:/^в/i,night:/^н/i}},Q0={ordinalNumber:C0({matchPattern:B0,parsePattern:G0,valueCallback:function G(B){return parseInt(B,10)}}),era:x({matchPatterns:H0,defaultMatchWidth:"wide",parsePatterns:X0,defaultParseWidth:"any"}),quarter:x({matchPatterns:E0,defaultMatchWidth:"wide",parsePatterns:J0,defaultParseWidth:"any",valueCallback:function G(B){return B+1}}),month:x({matchPatterns:U0,defaultMatchWidth:"wide",parsePatterns:Y0,defaultParseWidth:"any"}),day:x({matchPatterns:Z0,defaultMatchWidth:"wide",parsePatterns:A0,defaultParseWidth:"any"}),dayPeriod:x({matchPatterns:I0,defaultMatchWidth:"wide",parsePatterns:q0,defaultParseWidth:"any"})},K0={code:"uk",formatDistance:w,formatLong:f,formatRelative:c,localize:o,match:Q0,options:{weekStartsOn:1,firstWeekContainsDate:1}};window.dateFns=T(T({},window.dateFns),{},{locale:T(T({},(U=window.dateFns)===null||U===void 0?void 0:U.locale),{},{uk:K0})})})();

//# debugId=54DB544EBDF8B15D64756e2164756e21
