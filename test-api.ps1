# Todo API Test Script
Write-Host "========================================" -ForegroundColor Cyan
Write-Host "    Todo API Function Test" -ForegroundColor Cyan
Write-Host "========================================" -ForegroundColor Cyan
Write-Host ""

$baseUrl = "http://localhost:3001"

# Test health check
Write-Host "1. Testing health check..." -ForegroundColor Yellow
try {
    $health = Invoke-RestMethod -Uri "$baseUrl/health" -Method GET
    Write-Host "Success: Health check passed - $($health.message)" -ForegroundColor Green
} catch {
    Write-Host "Error: Health check failed - $($_.Exception.Message)" -ForegroundColor Red
    exit 1
}

# Test get all todos
Write-Host "`n2. Testing get all todos..." -ForegroundColor Yellow
try {
    $todos = Invoke-RestMethod -Uri "$baseUrl/api/todos" -Method GET
    Write-Host "Success: Got todos, count: $($todos.count)" -ForegroundColor Green
} catch {
    Write-Host "Error: Failed to get todos - $($_.Exception.Message)" -ForegroundColor Red
}

# Test create new todo
Write-Host "`n3. Testing create new todo..." -ForegroundColor Yellow
$newTodoJson = '{"title":"API Test Todo","description":"Test todo created by PowerShell script","priority":"medium"}'

try {
    $createdTodo = Invoke-RestMethod -Uri "$baseUrl/api/todos" -Method POST -Headers @{"Content-Type"="application/json"} -Body $newTodoJson
    $todoId = $createdTodo.data.id
    Write-Host "Success: Created todo with ID: $todoId" -ForegroundColor Green

    # Test get single todo
    Write-Host "`n4. Testing get single todo..." -ForegroundColor Yellow
    $singleTodo = Invoke-RestMethod -Uri "$baseUrl/api/todos/$todoId" -Method GET
    Write-Host "Success: Got single todo: $($singleTodo.data.title)" -ForegroundColor Green

    # Test update todo
    Write-Host "`n5. Testing update todo..." -ForegroundColor Yellow
    $updateJson = '{"title":"Updated API Test Todo","completed":true}'

    $updatedTodo = Invoke-RestMethod -Uri "$baseUrl/api/todos/$todoId" -Method PUT -Headers @{"Content-Type"="application/json"} -Body $updateJson
    Write-Host "Success: Updated todo, completed: $($updatedTodo.data.completed)" -ForegroundColor Green

    # Test toggle completion
    Write-Host "`n6. Testing toggle completion..." -ForegroundColor Yellow
    $toggledTodo = Invoke-RestMethod -Uri "$baseUrl/api/todos/$todoId/toggle" -Method PATCH
    Write-Host "Success: Toggled status, new status: $($toggledTodo.data.completed)" -ForegroundColor Green

    # Test delete todo
    Write-Host "`n7. Testing delete todo..." -ForegroundColor Yellow
    Invoke-RestMethod -Uri "$baseUrl/api/todos/$todoId" -Method DELETE
    Write-Host "Success: Deleted todo" -ForegroundColor Green

} catch {
    Write-Host "Error: Test failed - $($_.Exception.Message)" -ForegroundColor Red
}

# Final status check
Write-Host "`n8. Final status check..." -ForegroundColor Yellow
try {
    $finalTodos = Invoke-RestMethod -Uri "$baseUrl/api/todos" -Method GET
    Write-Host "Success: Final todo count: $($finalTodos.count)" -ForegroundColor Green
} catch {
    Write-Host "Error: Final check failed - $($_.Exception.Message)" -ForegroundColor Red
}

Write-Host "`n========================================" -ForegroundColor Cyan
Write-Host "    API Test Complete!" -ForegroundColor Cyan
Write-Host "========================================" -ForegroundColor Cyan
