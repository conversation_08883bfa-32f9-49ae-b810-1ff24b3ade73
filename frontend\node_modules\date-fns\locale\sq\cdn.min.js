var A=function(J){return A=typeof Symbol=="function"&&typeof Symbol.iterator=="symbol"?function(H){return typeof H}:function(H){return H&&typeof Symbol=="function"&&H.constructor===Symbol&&H!==Symbol.prototype?"symbol":typeof H},A(J)},D=function(J,H){var U=Object.keys(J);if(Object.getOwnPropertySymbols){var Z=Object.getOwnPropertySymbols(J);H&&(Z=Z.filter(function(N){return Object.getOwnPropertyDescriptor(J,N).enumerable})),U.push.apply(U,Z)}return U},K=function(J){for(var H=1;H<arguments.length;H++){var U=arguments[H]!=null?arguments[H]:{};H%2?D(Object(U),!0).forEach(function(Z){B1(J,Z,U[Z])}):Object.getOwnPropertyDescriptors?Object.defineProperties(J,Object.getOwnPropertyDescriptors(U)):D(Object(U)).forEach(function(Z){Object.defineProperty(J,Z,Object.getOwnPropertyDescriptor(U,Z))})}return J},B1=function(J,H,U){if(H=E1(H),H in J)Object.defineProperty(J,H,{value:U,enumerable:!0,configurable:!0,writable:!0});else J[H]=U;return J},E1=function(J){var H=C1(J,"string");return A(H)=="symbol"?H:String(H)},C1=function(J,H){if(A(J)!="object"||!J)return J;var U=J[Symbol.toPrimitive];if(U!==void 0){var Z=U.call(J,H||"default");if(A(Z)!="object")return Z;throw new TypeError("@@toPrimitive must return a primitive value.")}return(H==="string"?String:Number)(J)};(function(J){var H=Object.defineProperty,U=function B(G,C){for(var E in C)H(G,E,{get:C[E],enumerable:!0,configurable:!0,set:function X(Y){return C[E]=function(){return Y}}})},Z={lessThanXSeconds:{one:"m\xEB pak se nj\xEB sekond\xEB",other:"m\xEB pak se {{count}} sekonda"},xSeconds:{one:"1 sekond\xEB",other:"{{count}} sekonda"},halfAMinute:"gjys\xEBm minuti",lessThanXMinutes:{one:"m\xEB pak se nj\xEB minute",other:"m\xEB pak se {{count}} minuta"},xMinutes:{one:"1 minut\xEB",other:"{{count}} minuta"},aboutXHours:{one:"rreth 1 or\xEB",other:"rreth {{count}} or\xEB"},xHours:{one:"1 or\xEB",other:"{{count}} or\xEB"},xDays:{one:"1 dit\xEB",other:"{{count}} dit\xEB"},aboutXWeeks:{one:"rreth 1 jav\xEB",other:"rreth {{count}} jav\xEB"},xWeeks:{one:"1 jav\xEB",other:"{{count}} jav\xEB"},aboutXMonths:{one:"rreth 1 muaj",other:"rreth {{count}} muaj"},xMonths:{one:"1 muaj",other:"{{count}} muaj"},aboutXYears:{one:"rreth 1 vit",other:"rreth {{count}} vite"},xYears:{one:"1 vit",other:"{{count}} vite"},overXYears:{one:"mbi 1 vit",other:"mbi {{count}} vite"},almostXYears:{one:"pothuajse 1 vit",other:"pothuajse {{count}} vite"}},N=function B(G,C,E){var X,Y=Z[G];if(typeof Y==="string")X=Y;else if(C===1)X=Y.one;else X=Y.other.replace("{{count}}",String(C));if(E!==null&&E!==void 0&&E.addSuffix)if(E.comparison&&E.comparison>0)return"n\xEB "+X;else return X+" m\xEB par\xEB";return X};function W(B){return function(){var G=arguments.length>0&&arguments[0]!==void 0?arguments[0]:{},C=G.width?String(G.width):B.defaultWidth,E=B.formats[C]||B.formats[B.defaultWidth];return E}}var M={full:"EEEE, MMMM do, y",long:"MMMM do, y",medium:"MMM d, y",short:"MM/dd/yyyy"},S={full:"h:mm:ss a zzzz",long:"h:mm:ss a z",medium:"h:mm:ss a",short:"h:mm a"},$={full:"{{date}} 'n\xEB' {{time}}",long:"{{date}} 'n\xEB' {{time}}",medium:"{{date}}, {{time}}",short:"{{date}}, {{time}}"},R={date:W({formats:M,defaultWidth:"full"}),time:W({formats:S,defaultWidth:"full"}),dateTime:W({formats:$,defaultWidth:"full"})},L={lastWeek:"'t\xEB' eeee 'e shkuar n\xEB' p",yesterday:"'dje n\xEB' p",today:"'sot n\xEB' p",tomorrow:"'nes\xEBr n\xEB' p",nextWeek:"eeee 'at' p",other:"P"},V=function B(G,C,E,X){return L[G]};function T(B){return function(G,C){var E=C!==null&&C!==void 0&&C.context?String(C.context):"standalone",X;if(E==="formatting"&&B.formattingValues){var Y=B.defaultFormattingWidth||B.defaultWidth,x=C!==null&&C!==void 0&&C.width?String(C.width):Y;X=B.formattingValues[x]||B.formattingValues[Y]}else{var I=B.defaultWidth,q=C!==null&&C!==void 0&&C.width?String(C.width):B.defaultWidth;X=B.values[q]||B.values[I]}var O=B.argumentCallback?B.argumentCallback(G):G;return X[O]}}var j={narrow:["P","M"],abbreviated:["PK","MK"],wide:["Para Krishtit","Mbas Krishtit"]},f={narrow:["1","2","3","4"],abbreviated:["Q1","Q2","Q3","Q4"],wide:["4-mujori I","4-mujori II","4-mujori III","4-mujori IV"]},v={narrow:["J","S","M","P","M","Q","K","G","S","T","N","D"],abbreviated:["Jan","Shk","Mar","Pri","Maj","Qer","Kor","Gus","Sht","Tet","N\xEBn","Dhj"],wide:["Janar","Shkurt","Mars","Prill","Maj","Qershor","Korrik","Gusht","Shtator","Tetor","N\xEBntor","Dhjetor"]},P={narrow:["D","H","M","M","E","P","S"],short:["Di","H\xEB","Ma","M\xEB","En","Pr","Sh"],abbreviated:["Die","H\xEBn","Mar","M\xEBr","Enj","Pre","Sht"],wide:["Diel\xEB","H\xEBn\xEB","Mart\xEB","M\xEBrkur\xEB","Enjte","Premte","Shtun\xEB"]},w={narrow:{am:"p",pm:"m",midnight:"m",noon:"d",morning:"m\xEBngjes",afternoon:"dite",evening:"mbr\xEBmje",night:"nat\xEB"},abbreviated:{am:"PD",pm:"MD",midnight:"mesn\xEBt\xEB",noon:"drek",morning:"m\xEBngjes",afternoon:"mbasdite",evening:"mbr\xEBmje",night:"nat\xEB"},wide:{am:"p.d.",pm:"m.d.",midnight:"mesn\xEBt\xEB",noon:"drek",morning:"m\xEBngjes",afternoon:"mbasdite",evening:"mbr\xEBmje",night:"nat\xEB"}},_={narrow:{am:"p",pm:"m",midnight:"m",noon:"d",morning:"n\xEB m\xEBngjes",afternoon:"n\xEB mbasdite",evening:"n\xEB mbr\xEBmje",night:"n\xEB mesnat\xEB"},abbreviated:{am:"PD",pm:"MD",midnight:"mesnat\xEB",noon:"drek",morning:"n\xEB m\xEBngjes",afternoon:"n\xEB mbasdite",evening:"n\xEB mbr\xEBmje",night:"n\xEB mesnat\xEB"},wide:{am:"p.d.",pm:"m.d.",midnight:"mesnat\xEB",noon:"drek",morning:"n\xEB m\xEBngjes",afternoon:"n\xEB mbasdite",evening:"n\xEB mbr\xEBmje",night:"n\xEB mesnat\xEB"}},F=function B(G,C){var E=Number(G);if((C===null||C===void 0?void 0:C.unit)==="hour")return String(E);if(E===1)return E+"-r\xEB";if(E===4)return E+"t";return E+"-t\xEB"},k={ordinalNumber:F,era:T({values:j,defaultWidth:"wide"}),quarter:T({values:f,defaultWidth:"wide",argumentCallback:function B(G){return G-1}}),month:T({values:v,defaultWidth:"wide"}),day:T({values:P,defaultWidth:"wide"}),dayPeriod:T({values:w,defaultWidth:"wide",formattingValues:_,defaultFormattingWidth:"wide"})};function Q(B){return function(G){var C=arguments.length>1&&arguments[1]!==void 0?arguments[1]:{},E=C.width,X=E&&B.matchPatterns[E]||B.matchPatterns[B.defaultMatchWidth],Y=G.match(X);if(!Y)return null;var x=Y[0],I=E&&B.parsePatterns[E]||B.parsePatterns[B.defaultParseWidth],q=Array.isArray(I)?b(I,function(z){return z.test(x)}):h(I,function(z){return z.test(x)}),O;O=B.valueCallback?B.valueCallback(q):q,O=C.valueCallback?C.valueCallback(O):O;var t=G.slice(x.length);return{value:O,rest:t}}}var h=function B(G,C){for(var E in G)if(Object.prototype.hasOwnProperty.call(G,E)&&C(G[E]))return E;return},b=function B(G,C){for(var E=0;E<G.length;E++)if(C(G[E]))return E;return};function m(B){return function(G){var C=arguments.length>1&&arguments[1]!==void 0?arguments[1]:{},E=G.match(B.matchPattern);if(!E)return null;var X=E[0],Y=G.match(B.parsePattern);if(!Y)return null;var x=B.valueCallback?B.valueCallback(Y[0]):Y[0];x=C.valueCallback?C.valueCallback(x):x;var I=G.slice(X.length);return{value:x,rest:I}}}var c=/^(\d+)(-rë|-të|t|)?/i,y=/\d+/i,g={narrow:/^(p|m)/i,abbreviated:/^(b\.?\s?c\.?|b\.?\s?c\.?\s?e\.?|a\.?\s?d\.?|c\.?\s?e\.?)/i,wide:/^(para krishtit|mbas krishtit)/i},p={any:[/^b/i,/^(p|m)/i]},d={narrow:/^[1234]/i,abbreviated:/^q[1234]/i,wide:/^[1234]-mujori (i{1,3}|iv)/i},u={any:[/1/i,/2/i,/3/i,/4/i]},l={narrow:/^[jsmpqkftnd]/i,abbreviated:/^(jan|shk|mar|pri|maj|qer|kor|gus|sht|tet|nën|dhj)/i,wide:/^(janar|shkurt|mars|prill|maj|qershor|korrik|gusht|shtator|tetor|nëntor|dhjetor)/i},i={narrow:[/^j/i,/^s/i,/^m/i,/^p/i,/^m/i,/^q/i,/^k/i,/^g/i,/^s/i,/^t/i,/^n/i,/^d/i],any:[/^ja/i,/^shk/i,/^mar/i,/^pri/i,/^maj/i,/^qer/i,/^kor/i,/^gu/i,/^sht/i,/^tet/i,/^n/i,/^d/i]},n={narrow:/^[dhmeps]/i,short:/^(di|hë|ma|më|en|pr|sh)/i,abbreviated:/^(die|hën|mar|mër|enj|pre|sht)/i,wide:/^(dielë|hënë|martë|mërkurë|enjte|premte|shtunë)/i},s={narrow:[/^d/i,/^h/i,/^m/i,/^m/i,/^e/i,/^p/i,/^s/i],any:[/^d/i,/^h/i,/^ma/i,/^më/i,/^e/i,/^p/i,/^s/i]},o={narrow:/^(p|m|me|në (mëngjes|mbasdite|mbrëmje|mesnatë))/i,any:/^([pm]\.?\s?d\.?|drek|në (mëngjes|mbasdite|mbrëmje|mesnatë))/i},r={any:{am:/^p/i,pm:/^m/i,midnight:/^me/i,noon:/^dr/i,morning:/mëngjes/i,afternoon:/mbasdite/i,evening:/mbrëmje/i,night:/natë/i}},a={ordinalNumber:m({matchPattern:c,parsePattern:y,valueCallback:function B(G){return parseInt(G,10)}}),era:Q({matchPatterns:g,defaultMatchWidth:"wide",parsePatterns:p,defaultParseWidth:"any"}),quarter:Q({matchPatterns:d,defaultMatchWidth:"wide",parsePatterns:u,defaultParseWidth:"any",valueCallback:function B(G){return G+1}}),month:Q({matchPatterns:l,defaultMatchWidth:"wide",parsePatterns:i,defaultParseWidth:"any"}),day:Q({matchPatterns:n,defaultMatchWidth:"wide",parsePatterns:s,defaultParseWidth:"any"}),dayPeriod:Q({matchPatterns:o,defaultMatchWidth:"any",parsePatterns:r,defaultParseWidth:"any"})},e={code:"sq",formatDistance:N,formatLong:R,formatRelative:V,localize:k,match:a,options:{weekStartsOn:1,firstWeekContainsDate:1}};window.dateFns=K(K({},window.dateFns),{},{locale:K(K({},(J=window.dateFns)===null||J===void 0?void 0:J.locale),{},{sq:e})})})();

//# debugId=4ED4830CC0F7D80164756e2164756e21
