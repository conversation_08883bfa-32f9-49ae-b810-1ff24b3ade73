import React, { useState } from 'react';
import { FaPlus, FaFlag, FaCalendar } from 'react-icons/fa';
import { CreateTodoRequest, Priority } from '../types/todo';

interface AddTodoProps {
  onAdd: (todo: CreateTodoRequest) => void;
  isLoading?: boolean;
}

const AddTodo: React.FC<AddTodoProps> = ({ onAdd, isLoading = false }) => {
  const [title, setTitle] = useState('');
  const [description, setDescription] = useState('');
  const [priority, setPriority] = useState<Priority>('medium');
  const [dueDate, setDueDate] = useState('');
  const [showDetails, setShowDetails] = useState(false);

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!title.trim()) return;

    const newTodo: CreateTodoRequest = {
      title: title.trim(),
      description: description.trim() || undefined,
      priority,
      dueDate: dueDate || undefined
    };

    onAdd(newTodo);
    
    // Reset form
    setTitle('');
    setDescription('');
    setPriority('medium');
    setDueDate('');
    setShowDetails(false);
  };

  const getPriorityColor = (p: string) => {
    switch (p) {
      case 'high': return 'border-red-500 text-red-700 bg-red-50';
      case 'medium': return 'border-yellow-500 text-yellow-700 bg-yellow-50';
      case 'low': return 'border-green-500 text-green-700 bg-green-50';
      default: return 'border-gray-300 text-gray-700 bg-white';
    }
  };

  return (
    <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-4 mb-6">
      <form onSubmit={handleSubmit} className="space-y-4">
        {/* Title Input */}
        <div className="flex gap-2">
          <input
            type="text"
            value={title}
            onChange={(e) => setTitle(e.target.value)}
            placeholder="What needs to be done?"
            className="flex-1 px-4 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
            disabled={isLoading}
          />
          <button
            type="button"
            onClick={() => setShowDetails(!showDetails)}
            className="px-3 py-2 text-gray-500 hover:text-gray-700 transition-colors"
            title="More options"
          >
            <FaPlus className={`transform transition-transform ${showDetails ? 'rotate-45' : ''}`} />
          </button>
          <button
            type="submit"
            disabled={!title.trim() || isLoading}
            className="px-6 py-2 bg-blue-500 text-white rounded-lg hover:bg-blue-600 disabled:opacity-50 disabled:cursor-not-allowed transition-colors flex items-center gap-2"
          >
            {isLoading ? (
              <div className="w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin" />
            ) : (
              <FaPlus size={14} />
            )}
            Add
          </button>
        </div>

        {/* Expanded Options */}
        {showDetails && (
          <div className="space-y-3 pt-2 border-t border-gray-100">
            {/* Description */}
            <textarea
              value={description}
              onChange={(e) => setDescription(e.target.value)}
              placeholder="Description (optional)"
              className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent resize-none"
              rows={2}
              disabled={isLoading}
            />

            <div className="flex gap-4">
              {/* Priority */}
              <div className="flex items-center gap-2">
                <FaFlag className="text-gray-400" size={14} />
                <select
                  value={priority}
                  onChange={(e) => setPriority(e.target.value as Priority)}
                  className={`px-3 py-1 border rounded-lg text-sm focus:outline-none focus:ring-2 focus:ring-blue-500 ${getPriorityColor(priority)}`}
                  disabled={isLoading}
                >
                  <option value="low">Low Priority</option>
                  <option value="medium">Medium Priority</option>
                  <option value="high">High Priority</option>
                </select>
              </div>

              {/* Due Date */}
              <div className="flex items-center gap-2">
                <FaCalendar className="text-gray-400" size={14} />
                <input
                  type="date"
                  value={dueDate}
                  onChange={(e) => setDueDate(e.target.value)}
                  min={new Date().toISOString().split('T')[0]}
                  className="px-3 py-1 border border-gray-300 rounded-lg text-sm focus:outline-none focus:ring-2 focus:ring-blue-500"
                  disabled={isLoading}
                />
              </div>
            </div>
          </div>
        )}
      </form>
    </div>
  );
};

export default AddTodo;
