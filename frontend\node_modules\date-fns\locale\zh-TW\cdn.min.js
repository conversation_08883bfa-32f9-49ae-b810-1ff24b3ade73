var A=function(J){return A=typeof Symbol=="function"&&typeof Symbol.iterator=="symbol"?function(H){return typeof H}:function(H){return H&&typeof Symbol=="function"&&H.constructor===Symbol&&H!==Symbol.prototype?"symbol":typeof H},A(J)},W=function(J,H){var U=Object.keys(J);if(Object.getOwnPropertySymbols){var Z=Object.getOwnPropertySymbols(J);H&&(Z=Z.filter(function(x){return Object.getOwnPropertyDescriptor(J,x).enumerable})),U.push.apply(U,Z)}return U},N=function(J){for(var H=1;H<arguments.length;H++){var U=arguments[H]!=null?arguments[H]:{};H%2?W(Object(U),!0).forEach(function(Z){C6(J,Z,U[Z])}):Object.getOwnPropertyDescriptors?Object.defineProperties(J,Object.getOwnPropertyDescriptors(U)):W(Object(U)).forEach(function(Z){Object.defineProperty(J,Z,Object.getOwnPropertyDescriptor(U,Z))})}return J},C6=function(J,H,U){if(H=E6(H),H in J)Object.defineProperty(J,H,{value:U,enumerable:!0,configurable:!0,writable:!0});else J[H]=U;return J},E6=function(J){var H=B6(J,"string");return A(H)=="symbol"?H:String(H)},B6=function(J,H){if(A(J)!="object"||!J)return J;var U=J[Symbol.toPrimitive];if(U!==void 0){var Z=U.call(J,H||"default");if(A(Z)!="object")return Z;throw new TypeError("@@toPrimitive must return a primitive value.")}return(H==="string"?String:Number)(J)};(function(J){var H=Object.defineProperty,U=function C(G,E){for(var B in E)H(G,B,{get:E[B],enumerable:!0,configurable:!0,set:function X(Y){return E[B]=function(){return Y}}})},Z={lessThanXSeconds:{one:"\u5C11\u65BC 1 \u79D2",other:"\u5C11\u65BC {{count}} \u79D2"},xSeconds:{one:"1 \u79D2",other:"{{count}} \u79D2"},halfAMinute:"\u534A\u5206\u9418",lessThanXMinutes:{one:"\u5C11\u65BC 1 \u5206\u9418",other:"\u5C11\u65BC {{count}} \u5206\u9418"},xMinutes:{one:"1 \u5206\u9418",other:"{{count}} \u5206\u9418"},xHours:{one:"1 \u5C0F\u6642",other:"{{count}} \u5C0F\u6642"},aboutXHours:{one:"\u5927\u7D04 1 \u5C0F\u6642",other:"\u5927\u7D04 {{count}} \u5C0F\u6642"},xDays:{one:"1 \u5929",other:"{{count}} \u5929"},aboutXWeeks:{one:"\u5927\u7D04 1 \u500B\u661F\u671F",other:"\u5927\u7D04 {{count}} \u500B\u661F\u671F"},xWeeks:{one:"1 \u500B\u661F\u671F",other:"{{count}} \u500B\u661F\u671F"},aboutXMonths:{one:"\u5927\u7D04 1 \u500B\u6708",other:"\u5927\u7D04 {{count}} \u500B\u6708"},xMonths:{one:"1 \u500B\u6708",other:"{{count}} \u500B\u6708"},aboutXYears:{one:"\u5927\u7D04 1 \u5E74",other:"\u5927\u7D04 {{count}} \u5E74"},xYears:{one:"1 \u5E74",other:"{{count}} \u5E74"},overXYears:{one:"\u8D85\u904E 1 \u5E74",other:"\u8D85\u904E {{count}} \u5E74"},almostXYears:{one:"\u5C07\u8FD1 1 \u5E74",other:"\u5C07\u8FD1 {{count}} \u5E74"}},x=function C(G,E,B){var X,Y=Z[G];if(typeof Y==="string")X=Y;else if(E===1)X=Y.one;else X=Y.other.replace("{{count}}",String(E));if(B!==null&&B!==void 0&&B.addSuffix)if(B.comparison&&B.comparison>0)return X+"\u5167";else return X+"\u524D";return X};function S(C){return function(){var G=arguments.length>0&&arguments[0]!==void 0?arguments[0]:{},E=G.width?String(G.width):C.defaultWidth,B=C.formats[E]||C.formats[C.defaultWidth];return B}}var $={full:"y'\u5E74'M'\u6708'd'\u65E5' EEEE",long:"y'\u5E74'M'\u6708'd'\u65E5'",medium:"yyyy-MM-dd",short:"yy-MM-dd"},z={full:"zzzz a h:mm:ss",long:"z a h:mm:ss",medium:"a h:mm:ss",short:"a h:mm"},M={full:"{{date}} {{time}}",long:"{{date}} {{time}}",medium:"{{date}} {{time}}",short:"{{date}} {{time}}"},R={date:S({formats:$,defaultWidth:"full"}),time:S({formats:z,defaultWidth:"full"}),dateTime:S({formats:M,defaultWidth:"full"})},L={lastWeek:"'\u4E0A\u500B'eeee p",yesterday:"'\u6628\u5929' p",today:"'\u4ECA\u5929' p",tomorrow:"'\u660E\u5929' p",nextWeek:"'\u4E0B\u500B'eeee p",other:"P"},V=function C(G,E,B,X){return L[G]};function O(C){return function(G,E){var B=E!==null&&E!==void 0&&E.context?String(E.context):"standalone",X;if(B==="formatting"&&C.formattingValues){var Y=C.defaultFormattingWidth||C.defaultWidth,I=E!==null&&E!==void 0&&E.width?String(E.width):Y;X=C.formattingValues[I]||C.formattingValues[Y]}else{var Q=C.defaultWidth,K=E!==null&&E!==void 0&&E.width?String(E.width):C.defaultWidth;X=C.values[K]||C.values[Q]}var T=C.argumentCallback?C.argumentCallback(G):G;return X[T]}}var j={narrow:["\u524D","\u516C\u5143"],abbreviated:["\u524D","\u516C\u5143"],wide:["\u516C\u5143\u524D","\u516C\u5143"]},f={narrow:["1","2","3","4"],abbreviated:["\u7B2C\u4E00\u523B","\u7B2C\u4E8C\u523B","\u7B2C\u4E09\u523B","\u7B2C\u56DB\u523B"],wide:["\u7B2C\u4E00\u523B\u9418","\u7B2C\u4E8C\u523B\u9418","\u7B2C\u4E09\u523B\u9418","\u7B2C\u56DB\u523B\u9418"]},v={narrow:["\u4E00","\u4E8C","\u4E09","\u56DB","\u4E94","\u516D","\u4E03","\u516B","\u4E5D","\u5341","\u5341\u4E00","\u5341\u4E8C"],abbreviated:["1\u6708","2\u6708","3\u6708","4\u6708","5\u6708","6\u6708","7\u6708","8\u6708","9\u6708","10\u6708","11\u6708","12\u6708"],wide:["\u4E00\u6708","\u4E8C\u6708","\u4E09\u6708","\u56DB\u6708","\u4E94\u6708","\u516D\u6708","\u4E03\u6708","\u516B\u6708","\u4E5D\u6708","\u5341\u6708","\u5341\u4E00\u6708","\u5341\u4E8C\u6708"]},w={narrow:["\u65E5","\u4E00","\u4E8C","\u4E09","\u56DB","\u4E94","\u516D"],short:["\u65E5","\u4E00","\u4E8C","\u4E09","\u56DB","\u4E94","\u516D"],abbreviated:["\u9031\u65E5","\u9031\u4E00","\u9031\u4E8C","\u9031\u4E09","\u9031\u56DB","\u9031\u4E94","\u9031\u516D"],wide:["\u661F\u671F\u65E5","\u661F\u671F\u4E00","\u661F\u671F\u4E8C","\u661F\u671F\u4E09","\u661F\u671F\u56DB","\u661F\u671F\u4E94","\u661F\u671F\u516D"]},P={narrow:{am:"\u4E0A",pm:"\u4E0B",midnight:"\u51CC\u6668",noon:"\u5348",morning:"\u65E9",afternoon:"\u4E0B\u5348",evening:"\u665A",night:"\u591C"},abbreviated:{am:"\u4E0A\u5348",pm:"\u4E0B\u5348",midnight:"\u51CC\u6668",noon:"\u4E2D\u5348",morning:"\u65E9\u6668",afternoon:"\u4E2D\u5348",evening:"\u665A\u4E0A",night:"\u591C\u9593"},wide:{am:"\u4E0A\u5348",pm:"\u4E0B\u5348",midnight:"\u51CC\u6668",noon:"\u4E2D\u5348",morning:"\u65E9\u6668",afternoon:"\u4E2D\u5348",evening:"\u665A\u4E0A",night:"\u591C\u9593"}},_={narrow:{am:"\u4E0A",pm:"\u4E0B",midnight:"\u51CC\u6668",noon:"\u5348",morning:"\u65E9",afternoon:"\u4E0B\u5348",evening:"\u665A",night:"\u591C"},abbreviated:{am:"\u4E0A\u5348",pm:"\u4E0B\u5348",midnight:"\u51CC\u6668",noon:"\u4E2D\u5348",morning:"\u65E9\u6668",afternoon:"\u4E2D\u5348",evening:"\u665A\u4E0A",night:"\u591C\u9593"},wide:{am:"\u4E0A\u5348",pm:"\u4E0B\u5348",midnight:"\u51CC\u6668",noon:"\u4E2D\u5348",morning:"\u65E9\u6668",afternoon:"\u4E2D\u5348",evening:"\u665A\u4E0A",night:"\u591C\u9593"}},F=function C(G,E){var B=Number(G);switch(E===null||E===void 0?void 0:E.unit){case"date":return B+"\u65E5";case"hour":return B+"\u6642";case"minute":return B+"\u5206";case"second":return B+"\u79D2";default:return"\u7B2C "+B}},k={ordinalNumber:F,era:O({values:j,defaultWidth:"wide"}),quarter:O({values:f,defaultWidth:"wide",argumentCallback:function C(G){return G-1}}),month:O({values:v,defaultWidth:"wide"}),day:O({values:w,defaultWidth:"wide"}),dayPeriod:O({values:P,defaultWidth:"wide",formattingValues:_,defaultFormattingWidth:"wide"})};function q(C){return function(G){var E=arguments.length>1&&arguments[1]!==void 0?arguments[1]:{},B=E.width,X=B&&C.matchPatterns[B]||C.matchPatterns[C.defaultMatchWidth],Y=G.match(X);if(!Y)return null;var I=Y[0],Q=B&&C.parsePatterns[B]||C.parsePatterns[C.defaultParseWidth],K=Array.isArray(Q)?h(Q,function(D){return D.test(I)}):b(Q,function(D){return D.test(I)}),T;T=C.valueCallback?C.valueCallback(K):K,T=E.valueCallback?E.valueCallback(T):T;var t=G.slice(I.length);return{value:T,rest:t}}}var b=function C(G,E){for(var B in G)if(Object.prototype.hasOwnProperty.call(G,B)&&E(G[B]))return B;return},h=function C(G,E){for(var B=0;B<G.length;B++)if(E(G[B]))return B;return};function m(C){return function(G){var E=arguments.length>1&&arguments[1]!==void 0?arguments[1]:{},B=G.match(C.matchPattern);if(!B)return null;var X=B[0],Y=G.match(C.parsePattern);if(!Y)return null;var I=C.valueCallback?C.valueCallback(Y[0]):Y[0];I=E.valueCallback?E.valueCallback(I):I;var Q=G.slice(X.length);return{value:I,rest:Q}}}var y=/^(第\s*)?\d+(日|時|分|秒)?/i,c=/\d+/i,u={narrow:/^(前)/i,abbreviated:/^(前)/i,wide:/^(公元前|公元)/i},d={any:[/^(前)/i,/^(公元)/i]},g={narrow:/^[1234]/i,abbreviated:/^第[一二三四]刻/i,wide:/^第[一二三四]刻鐘/i},p={any:[/(1|一)/i,/(2|二)/i,/(3|三)/i,/(4|四)/i]},l={narrow:/^(一|二|三|四|五|六|七|八|九|十[二一])/i,abbreviated:/^(一|二|三|四|五|六|七|八|九|十[二一]|\d|1[12])月/i,wide:/^(一|二|三|四|五|六|七|八|九|十[二一])月/i},i={narrow:[/^一/i,/^二/i,/^三/i,/^四/i,/^五/i,/^六/i,/^七/i,/^八/i,/^九/i,/^十(?!(一|二))/i,/^十一/i,/^十二/i],any:[/^一|1/i,/^二|2/i,/^三|3/i,/^四|4/i,/^五|5/i,/^六|6/i,/^七|7/i,/^八|8/i,/^九|9/i,/^十(?!(一|二))|10/i,/^十一|11/i,/^十二|12/i]},n={narrow:/^[一二三四五六日]/i,short:/^[一二三四五六日]/i,abbreviated:/^週[一二三四五六日]/i,wide:/^星期[一二三四五六日]/i},s={any:[/日/i,/一/i,/二/i,/三/i,/四/i,/五/i,/六/i]},o={any:/^(上午?|下午?|午夜|[中正]午|早上?|下午|晚上?|凌晨)/i},r={any:{am:/^上午?/i,pm:/^下午?/i,midnight:/^午夜/i,noon:/^[中正]午/i,morning:/^早上/i,afternoon:/^下午/i,evening:/^晚上?/i,night:/^凌晨/i}},e={ordinalNumber:m({matchPattern:y,parsePattern:c,valueCallback:function C(G){return parseInt(G,10)}}),era:q({matchPatterns:u,defaultMatchWidth:"wide",parsePatterns:d,defaultParseWidth:"any"}),quarter:q({matchPatterns:g,defaultMatchWidth:"wide",parsePatterns:p,defaultParseWidth:"any",valueCallback:function C(G){return G+1}}),month:q({matchPatterns:l,defaultMatchWidth:"wide",parsePatterns:i,defaultParseWidth:"any"}),day:q({matchPatterns:n,defaultMatchWidth:"wide",parsePatterns:s,defaultParseWidth:"any"}),dayPeriod:q({matchPatterns:o,defaultMatchWidth:"any",parsePatterns:r,defaultParseWidth:"any"})},a={code:"zh-TW",formatDistance:x,formatLong:R,formatRelative:V,localize:k,match:e,options:{weekStartsOn:1,firstWeekContainsDate:4}};window.dateFns=N(N({},window.dateFns),{},{locale:N(N({},(J=window.dateFns)===null||J===void 0?void 0:J.locale),{},{zhTW:a})})})();

//# debugId=AE066AB3FDE72DCE64756e2164756e21
