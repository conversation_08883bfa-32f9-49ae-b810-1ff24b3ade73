@echo off
echo ========================================
echo    待办事项列表应用 - 快速启动脚本
echo ========================================
echo.

echo 正在检查 MongoDB 连接...
echo 请确保 MongoDB 已经启动并运行在 localhost:27017
echo.

echo 启动后端服务器...
start "Backend Server" cmd /k "cd backend && npm run dev"

timeout /t 3 /nobreak >nul

echo 启动前端开发服务器...
start "Frontend Server" cmd /k "cd frontend && npm run dev"

echo.
echo ========================================
echo 服务启动完成！
echo.
echo 后端API: http://localhost:3001
echo 前端应用: http://localhost:5173
echo.
echo 按任意键退出...
echo ========================================
pause >nul
