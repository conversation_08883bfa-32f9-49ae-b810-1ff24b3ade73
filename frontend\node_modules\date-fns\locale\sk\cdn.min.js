var O=function(X,U){var A=Object.keys(X);if(Object.getOwnPropertySymbols){var G=Object.getOwnPropertySymbols(X);U&&(G=G.filter(function(N){return Object.getOwnPropertyDescriptor(X,N).enumerable})),A.push.apply(A,G)}return A},x=function(X){for(var U=1;U<arguments.length;U++){var A=arguments[U]!=null?arguments[U]:{};U%2?O(Object(A),!0).forEach(function(G){N1(X,G,A[G])}):Object.getOwnPropertyDescriptors?Object.defineProperties(X,Object.getOwnPropertyDescriptors(A)):O(Object(A)).forEach(function(G){Object.defineProperty(X,G,Object.getOwnPropertyDescriptor(A,G))})}return X},N1=function(X,U,A){if(U=M1(U),U in X)Object.defineProperty(X,U,{value:A,enumerable:!0,configurable:!0,writable:!0});else X[U]=A;return X},M1=function(X){var U=V1(X,"string");return z(U)=="symbol"?U:String(U)},V1=function(X,U){if(z(X)!="object"||!X)return X;var A=X[Symbol.toPrimitive];if(A!==void 0){var G=A.call(X,U||"default");if(z(G)!="object")return G;throw new TypeError("@@toPrimitive must return a primitive value.")}return(U==="string"?String:Number)(X)},z=function(X){return z=typeof Symbol=="function"&&typeof Symbol.iterator=="symbol"?function(U){return typeof U}:function(U){return U&&typeof Symbol=="function"&&U.constructor===Symbol&&U!==Symbol.prototype?"symbol":typeof U},z(X)};(function(X){var U=Object.defineProperty,A=function C(E,B){for(var H in B)U(E,H,{get:B[H],enumerable:!0,configurable:!0,set:function J(Y){return B[H]=function(){return Y}}})},G=function C(E,B){if(B===1&&E.one)return E.one;if(B>=2&&B<=4&&E.twoFour)return E.twoFour;return E.other},N=function C(E,B,H){var J=G(E,B),Y=J[H];return Y.replace("{{count}}",String(B))},F=function C(E){var B=["lessThan","about","over","almost"].filter(function(H){return!!E.match(new RegExp("^"+H))});return B[0]},D=function C(E){var B="";if(E==="almost")B="takmer";if(E==="about")B="pribli\u017Ene";return B.length>0?B+" ":""},R=function C(E){var B="";if(E==="lessThan")B="menej ne\u017E";if(E==="over")B="viac ne\u017E";return B.length>0?B+" ":""},P=function C(E){return E.charAt(0).toLowerCase()+E.slice(1)},b={xSeconds:{one:{present:"sekunda",past:"sekundou",future:"sekundu"},twoFour:{present:"{{count}} sekundy",past:"{{count}} sekundami",future:"{{count}} sekundy"},other:{present:"{{count}} sek\xFAnd",past:"{{count}} sekundami",future:"{{count}} sek\xFAnd"}},halfAMinute:{other:{present:"pol min\xFAty",past:"pol min\xFAtou",future:"pol min\xFAty"}},xMinutes:{one:{present:"min\xFAta",past:"min\xFAtou",future:"min\xFAtu"},twoFour:{present:"{{count}} min\xFAty",past:"{{count}} min\xFAtami",future:"{{count}} min\xFAty"},other:{present:"{{count}} min\xFAt",past:"{{count}} min\xFAtami",future:"{{count}} min\xFAt"}},xHours:{one:{present:"hodina",past:"hodinou",future:"hodinu"},twoFour:{present:"{{count}} hodiny",past:"{{count}} hodinami",future:"{{count}} hodiny"},other:{present:"{{count}} hod\xEDn",past:"{{count}} hodinami",future:"{{count}} hod\xEDn"}},xDays:{one:{present:"de\u0148",past:"d\u0148om",future:"de\u0148"},twoFour:{present:"{{count}} dni",past:"{{count}} d\u0148ami",future:"{{count}} dni"},other:{present:"{{count}} dn\xED",past:"{{count}} d\u0148ami",future:"{{count}} dn\xED"}},xWeeks:{one:{present:"t\xFD\u017Ede\u0148",past:"t\xFD\u017Ed\u0148om",future:"t\xFD\u017Ede\u0148"},twoFour:{present:"{{count}} t\xFD\u017Edne",past:"{{count}} t\xFD\u017Ed\u0148ami",future:"{{count}} t\xFD\u017Edne"},other:{present:"{{count}} t\xFD\u017Ed\u0148ov",past:"{{count}} t\xFD\u017Ed\u0148ami",future:"{{count}} t\xFD\u017Ed\u0148ov"}},xMonths:{one:{present:"mesiac",past:"mesiacom",future:"mesiac"},twoFour:{present:"{{count}} mesiace",past:"{{count}} mesiacmi",future:"{{count}} mesiace"},other:{present:"{{count}} mesiacov",past:"{{count}} mesiacmi",future:"{{count}} mesiacov"}},xYears:{one:{present:"rok",past:"rokom",future:"rok"},twoFour:{present:"{{count}} roky",past:"{{count}} rokmi",future:"{{count}} roky"},other:{present:"{{count}} rokov",past:"{{count}} rokmi",future:"{{count}} rokov"}}},w=function C(E,B,H){var J=F(E)||"",Y=P(E.substring(J.length)),Z=b[Y];if(!(H!==null&&H!==void 0&&H.addSuffix))return D(J)+R(J)+N(Z,B,"present");if(H.comparison&&H.comparison>0)return D(J)+"o "+R(J)+N(Z,B,"future");else return D(J)+"pred "+R(J)+N(Z,B,"past")};function L(C){return function(){var E=arguments.length>0&&arguments[0]!==void 0?arguments[0]:{},B=E.width?String(E.width):C.defaultWidth,H=C.formats[B]||C.formats[C.defaultWidth];return H}}var _={full:"EEEE d. MMMM y",long:"d. MMMM y",medium:"d. M. y",short:"d. M. y"},h={full:"H:mm:ss zzzz",long:"H:mm:ss z",medium:"H:mm:ss",short:"H:mm"},k={full:"{{date}}, {{time}}",long:"{{date}}, {{time}}",medium:"{{date}}, {{time}}",short:"{{date}} {{time}}"},f={date:L({formats:_,defaultWidth:"full"}),time:L({formats:h,defaultWidth:"full"}),dateTime:L({formats:k,defaultWidth:"full"})};function m(C){var E=Object.prototype.toString.call(C);if(C instanceof Date||z(C)==="object"&&E==="[object Date]")return new C.constructor(+C);else if(typeof C==="number"||E==="[object Number]"||typeof C==="string"||E==="[object String]")return new Date(C);else return new Date(NaN)}function y(){return W}function x1(C){W=C}var W={};function S(C,E){var B,H,J,Y,Z,I,Q=y(),T=(B=(H=(J=(Y=E===null||E===void 0?void 0:E.weekStartsOn)!==null&&Y!==void 0?Y:E===null||E===void 0||(Z=E.locale)===null||Z===void 0||(Z=Z.options)===null||Z===void 0?void 0:Z.weekStartsOn)!==null&&J!==void 0?J:Q.weekStartsOn)!==null&&H!==void 0?H:(I=Q.locale)===null||I===void 0||(I=I.options)===null||I===void 0?void 0:I.weekStartsOn)!==null&&B!==void 0?B:0,q=m(C),K=q.getDay(),z1=(K<T?7:0)+K-T;return q.setDate(q.getDate()-z1),q.setHours(0,0,0,0),q}function $(C,E,B){var H=S(C,B),J=S(E,B);return+H===+J}var c=function C(E){var B=j[E];switch(E){case 0:case 3:case 6:return"'minul\xFA "+B+" o' p";default:return"'minul\xFD' eeee 'o' p"}},v=function C(E){var B=j[E];if(E===4)return"'vo' eeee 'o' p";else return"'v "+B+" o' p"},g=function C(E){var B=j[E];switch(E){case 0:case 4:case 6:return"'bud\xFAcu "+B+" o' p";default:return"'bud\xFAci' eeee 'o' p"}},j=["nede\u013Eu","pondelok","utorok","stredu","\u0161tvrtok","piatok","sobotu"],u={lastWeek:function C(E,B,H){var J=E.getDay();if($(E,B,H))return v(J);else return c(J)},yesterday:"'v\u010Dera o' p",today:"'dnes o' p",tomorrow:"'zajtra o' p",nextWeek:function C(E,B,H){var J=E.getDay();if($(E,B,H))return v(J);else return g(J)},other:"P"},l=function C(E,B,H,J){var Y=u[E];if(typeof Y==="function")return Y(B,H,J);return Y};function M(C){return function(E,B){var H=B!==null&&B!==void 0&&B.context?String(B.context):"standalone",J;if(H==="formatting"&&C.formattingValues){var Y=C.defaultFormattingWidth||C.defaultWidth,Z=B!==null&&B!==void 0&&B.width?String(B.width):Y;J=C.formattingValues[Z]||C.formattingValues[Y]}else{var I=C.defaultWidth,Q=B!==null&&B!==void 0&&B.width?String(B.width):C.defaultWidth;J=C.values[Q]||C.values[I]}var T=C.argumentCallback?C.argumentCallback(E):E;return J[T]}}var d={narrow:["pred Kr.","po Kr."],abbreviated:["pred Kr.","po Kr."],wide:["pred Kristom","po Kristovi"]},p={narrow:["1","2","3","4"],abbreviated:["Q1","Q2","Q3","Q4"],wide:["1. \u0161tvr\u0165rok","2. \u0161tvr\u0165rok","3. \u0161tvr\u0165rok","4. \u0161tvr\u0165rok"]},i={narrow:["j","f","m","a","m","j","j","a","s","o","n","d"],abbreviated:["jan","feb","mar","apr","m\xE1j","j\xFAn","j\xFAl","aug","sep","okt","nov","dec"],wide:["janu\xE1r","febru\xE1r","marec","apr\xEDl","m\xE1j","j\xFAn","j\xFAl","august","september","okt\xF3ber","november","december"]},n={narrow:["j","f","m","a","m","j","j","a","s","o","n","d"],abbreviated:["jan","feb","mar","apr","m\xE1j","j\xFAn","j\xFAl","aug","sep","okt","nov","dec"],wide:["janu\xE1ra","febru\xE1ra","marca","apr\xEDla","m\xE1ja","j\xFAna","j\xFAla","augusta","septembra","okt\xF3bra","novembra","decembra"]},r={narrow:["n","p","u","s","\u0161","p","s"],short:["ne","po","ut","st","\u0161t","pi","so"],abbreviated:["ne","po","ut","st","\u0161t","pi","so"],wide:["nede\u013Ea","pondelok","utorok","streda","\u0161tvrtok","piatok","sobota"]},s={narrow:{am:"AM",pm:"PM",midnight:"poln.",noon:"pol.",morning:"r\xE1no",afternoon:"pop.",evening:"ve\u010D.",night:"noc"},abbreviated:{am:"AM",pm:"PM",midnight:"poln.",noon:"pol.",morning:"r\xE1no",afternoon:"popol.",evening:"ve\u010Der",night:"noc"},wide:{am:"AM",pm:"PM",midnight:"polnoc",noon:"poludnie",morning:"r\xE1no",afternoon:"popoludnie",evening:"ve\u010Der",night:"noc"}},o={narrow:{am:"AM",pm:"PM",midnight:"o poln.",noon:"nap.",morning:"r\xE1no",afternoon:"pop.",evening:"ve\u010D.",night:"v n."},abbreviated:{am:"AM",pm:"PM",midnight:"o poln.",noon:"napol.",morning:"r\xE1no",afternoon:"popol.",evening:"ve\u010Der",night:"v noci"},wide:{am:"AM",pm:"PM",midnight:"o polnoci",noon:"napoludnie",morning:"r\xE1no",afternoon:"popoludn\xED",evening:"ve\u010Der",night:"v noci"}},a=function C(E,B){var H=Number(E);return H+"."},e={ordinalNumber:a,era:M({values:d,defaultWidth:"wide"}),quarter:M({values:p,defaultWidth:"wide",argumentCallback:function C(E){return E-1}}),month:M({values:i,defaultWidth:"wide",formattingValues:n,defaultFormattingWidth:"wide"}),day:M({values:r,defaultWidth:"wide"}),dayPeriod:M({values:s,defaultWidth:"wide",formattingValues:o,defaultFormattingWidth:"wide"})};function V(C){return function(E){var B=arguments.length>1&&arguments[1]!==void 0?arguments[1]:{},H=B.width,J=H&&C.matchPatterns[H]||C.matchPatterns[C.defaultMatchWidth],Y=E.match(J);if(!Y)return null;var Z=Y[0],I=H&&C.parsePatterns[H]||C.parsePatterns[C.defaultParseWidth],Q=Array.isArray(I)?E1(I,function(K){return K.test(Z)}):t(I,function(K){return K.test(Z)}),T;T=C.valueCallback?C.valueCallback(Q):Q,T=B.valueCallback?B.valueCallback(T):T;var q=E.slice(Z.length);return{value:T,rest:q}}}var t=function C(E,B){for(var H in E)if(Object.prototype.hasOwnProperty.call(E,H)&&B(E[H]))return H;return},E1=function C(E,B){for(var H=0;H<E.length;H++)if(B(E[H]))return H;return};function B1(C){return function(E){var B=arguments.length>1&&arguments[1]!==void 0?arguments[1]:{},H=E.match(C.matchPattern);if(!H)return null;var J=H[0],Y=E.match(C.parsePattern);if(!Y)return null;var Z=C.valueCallback?C.valueCallback(Y[0]):Y[0];Z=B.valueCallback?B.valueCallback(Z):Z;var I=E.slice(J.length);return{value:Z,rest:I}}}var C1=/^(\d+)\.?/i,H1=/\d+/i,J1={narrow:/^(pred Kr\.|pred n\. l\.|po Kr\.|n\. l\.)/i,abbreviated:/^(pred Kr\.|pred n\. l\.|po Kr\.|n\. l\.)/i,wide:/^(pred Kristom|pred na[šs][íi]m letopo[čc]tom|po Kristovi|n[áa][šs]ho letopo[čc]tu)/i},U1={any:[/^pr/i,/^(po|n)/i]},X1={narrow:/^[1234]/i,abbreviated:/^q[1234]/i,wide:/^[1234]\. [šs]tvr[ťt]rok/i},Y1={any:[/1/i,/2/i,/3/i,/4/i]},Z1={narrow:/^[jfmasond]/i,abbreviated:/^(jan|feb|mar|apr|m[áa]j|j[úu]n|j[úu]l|aug|sep|okt|nov|dec)/i,wide:/^(janu[áa]ra?|febru[áa]ra?|(marec|marca)|apr[íi]la?|m[áa]ja?|j[úu]na?|j[úu]la?|augusta?|(september|septembra)|(okt[óo]ber|okt[óo]bra)|(november|novembra)|(december|decembra))/i},A1={narrow:[/^j/i,/^f/i,/^m/i,/^a/i,/^m/i,/^j/i,/^j/i,/^a/i,/^s/i,/^o/i,/^n/i,/^d/i],any:[/^ja/i,/^f/i,/^mar/i,/^ap/i,/^m[áa]j/i,/^j[úu]n/i,/^j[úu]l/i,/^au/i,/^s/i,/^o/i,/^n/i,/^d/i]},G1={narrow:/^[npusšp]/i,short:/^(ne|po|ut|st|št|pi|so)/i,abbreviated:/^(ne|po|ut|st|št|pi|so)/i,wide:/^(nede[ľl]a|pondelok|utorok|streda|[šs]tvrtok|piatok|sobota])/i},I1={narrow:[/^n/i,/^p/i,/^u/i,/^s/i,/^š/i,/^p/i,/^s/i],any:[/^n/i,/^po/i,/^u/i,/^st/i,/^(št|stv)/i,/^pi/i,/^so/i]},T1={narrow:/^(am|pm|(o )?poln\.?|(nap\.?|pol\.?)|r[áa]no|pop\.?|ve[čc]\.?|(v n\.?|noc))/i,abbreviated:/^(am|pm|(o )?poln\.?|(napol\.?|pol\.?)|r[áa]no|pop\.?|ve[čc]er|(v )?noci?)/i,any:/^(am|pm|(o )?polnoci?|(na)?poludnie|r[áa]no|popoludn(ie|í|i)|ve[čc]er|(v )?noci?)/i},Q1={any:{am:/^am/i,pm:/^pm/i,midnight:/poln/i,noon:/^(nap|(na)?pol(\.|u))/i,morning:/^r[áa]no/i,afternoon:/^pop/i,evening:/^ve[čc]/i,night:/^(noc|v n\.)/i}},q1={ordinalNumber:B1({matchPattern:C1,parsePattern:H1,valueCallback:function C(E){return parseInt(E,10)}}),era:V({matchPatterns:J1,defaultMatchWidth:"wide",parsePatterns:U1,defaultParseWidth:"any"}),quarter:V({matchPatterns:X1,defaultMatchWidth:"wide",parsePatterns:Y1,defaultParseWidth:"any",valueCallback:function C(E){return E+1}}),month:V({matchPatterns:Z1,defaultMatchWidth:"wide",parsePatterns:A1,defaultParseWidth:"any"}),day:V({matchPatterns:G1,defaultMatchWidth:"wide",parsePatterns:I1,defaultParseWidth:"any"}),dayPeriod:V({matchPatterns:T1,defaultMatchWidth:"any",parsePatterns:Q1,defaultParseWidth:"any"})},K1={code:"sk",formatDistance:w,formatLong:f,formatRelative:l,localize:e,match:q1,options:{weekStartsOn:1,firstWeekContainsDate:4}};window.dateFns=x(x({},window.dateFns),{},{locale:x(x({},(X=window.dateFns)===null||X===void 0?void 0:X.locale),{},{sk:K1})})})();

//# debugId=AA7C343EFBF831E064756e2164756e21
