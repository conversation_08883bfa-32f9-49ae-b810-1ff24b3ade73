// Priority type
export type Priority = 'low' | 'medium' | 'high';

// Filter type
export type FilterType = 'all' | 'active' | 'completed';

// Main Todo interface
export interface Todo {
  id: string;
  title: string;
  description?: string;
  completed: boolean;
  priority: Priority;
  dueDate?: string;
  createdAt: string;
  updatedAt: string;
}

// Request interfaces
export interface CreateTodoRequest {
  title: string;
  description?: string;
  priority?: Priority;
  dueDate?: string;
}

export interface UpdateTodoRequest {
  title?: string;
  description?: string;
  completed?: boolean;
  priority?: Priority;
  dueDate?: string;
}

// Response interfaces
export interface TodosResponse {
  success: boolean;
  count: number;
  data: Todo[];
}

export interface TodoResponse {
  success: boolean;
  data: Todo;
}

// Filter interface
export interface TodoFilter {
  completed?: boolean;
  priority?: Priority;
  sort?: string;
}
